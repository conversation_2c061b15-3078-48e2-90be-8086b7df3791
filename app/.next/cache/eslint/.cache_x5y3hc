[{"/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/layout.tsx": "1", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/page.tsx": "2", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/AuthDialog.tsx": "3", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/LoginForm.tsx": "4", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/SignUpForm.tsx": "5", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx": "6", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundLibrary.tsx": "7", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundPanel.tsx": "8", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Canvas.tsx": "9", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx": "10", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/LayerPanel.tsx": "11", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectLibrary.tsx": "12", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectPanel.tsx": "13", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPanel.tsx": "14", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPresets.tsx": "15", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Toolbar.tsx": "16", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/export/ExportDialog.tsx": "17", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SaveTemplateDialog.tsx": "18", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SubmitTemplateDialog.tsx": "19", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/TemplateLibrary.tsx": "20", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/alert.tsx": "21", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/avatar.tsx": "22", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/badge.tsx": "23", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/button.tsx": "24", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/card.tsx": "25", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dialog.tsx": "26", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dropdown-menu.tsx": "27", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/input.tsx": "28", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/label.tsx": "29", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/select.tsx": "30", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/slider.tsx": "31", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/tabs.tsx": "32", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/textarea.tsx": "33", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useAuth.ts": "34", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useEditor.ts": "35", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/database.ts": "36", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/exportService.ts": "37", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase-server.ts": "38", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase.ts": "39", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/templateService.ts": "40", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/utils.ts": "41", "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/types/index.ts": "42"}, {"size": 851, "mtime": 1753788536209, "results": "43", "hashOfConfig": "44"}, {"size": 777, "mtime": 1753790243708, "results": "45", "hashOfConfig": "44"}, {"size": 1002, "mtime": 1753787121286, "results": "46", "hashOfConfig": "44"}, {"size": 3003, "mtime": 1753787082785, "results": "47", "hashOfConfig": "44"}, {"size": 5249, "mtime": 1753787113425, "results": "48", "hashOfConfig": "44"}, {"size": 3717, "mtime": 1753787137447, "results": "49", "hashOfConfig": "44"}, {"size": 6678, "mtime": 1753789361524, "results": "50", "hashOfConfig": "44"}, {"size": 9643, "mtime": 1753789311100, "results": "51", "hashOfConfig": "44"}, {"size": 7445, "mtime": 1753789402527, "results": "52", "hashOfConfig": "44"}, {"size": 1492, "mtime": 1753789619913, "results": "53", "hashOfConfig": "44"}, {"size": 5594, "mtime": 1753788714508, "results": "54", "hashOfConfig": "44"}, {"size": 7036, "mtime": 1753789564796, "results": "55", "hashOfConfig": "44"}, {"size": 9817, "mtime": 1753789587128, "results": "56", "hashOfConfig": "44"}, {"size": 8752, "mtime": 1753789128602, "results": "57", "hashOfConfig": "44"}, {"size": 5103, "mtime": 1753789191445, "results": "58", "hashOfConfig": "44"}, {"size": 5938, "mtime": 1753790194673, "results": "59", "hashOfConfig": "44"}, {"size": 12348, "mtime": 1753790145065, "results": "60", "hashOfConfig": "44"}, {"size": 8258, "mtime": 1753789784541, "results": "61", "hashOfConfig": "44"}, {"size": 4363, "mtime": 1753790531150, "results": "62", "hashOfConfig": "44"}, {"size": 10176, "mtime": 1753789750801, "results": "63", "hashOfConfig": "44"}, {"size": 1583, "mtime": 1753787212600, "results": "64", "hashOfConfig": "44"}, {"size": 1404, "mtime": 1753788507964, "results": "65", "hashOfConfig": "44"}, {"size": 1127, "mtime": 1753789372026, "results": "66", "hashOfConfig": "44"}, {"size": 1834, "mtime": 1753787146979, "results": "67", "hashOfConfig": "44"}, {"size": 1876, "mtime": 1753787178991, "results": "68", "hashOfConfig": "44"}, {"size": 3833, "mtime": 1753787195793, "results": "69", "hashOfConfig": "44"}, {"size": 4977, "mtime": 1753788498099, "results": "70", "hashOfConfig": "44"}, {"size": 823, "mtime": 1753787163296, "results": "71", "hashOfConfig": "44"}, {"size": 709, "mtime": 1753787169710, "results": "72", "hashOfConfig": "44"}, {"size": 5612, "mtime": 1753789163732, "results": "73", "hashOfConfig": "44"}, {"size": 1076, "mtime": 1753789143912, "results": "74", "hashOfConfig": "44"}, {"size": 1882, "mtime": 1753789320716, "results": "75", "hashOfConfig": "44"}, {"size": 771, "mtime": 1753789791752, "results": "76", "hashOfConfig": "44"}, {"size": 3426, "mtime": 1753790956720, "results": "77", "hashOfConfig": "44"}, {"size": 11703, "mtime": 1753790972070, "results": "78", "hashOfConfig": "44"}, {"size": 5136, "mtime": 1753786001060, "results": "79", "hashOfConfig": "44"}, {"size": 7650, "mtime": 1753790096174, "results": "80", "hashOfConfig": "44"}, {"size": 2285, "mtime": 1753791011278, "results": "81", "hashOfConfig": "44"}, {"size": 322, "mtime": 1753790937834, "results": "82", "hashOfConfig": "44"}, {"size": 7105, "mtime": 1753789709779, "results": "83", "hashOfConfig": "44"}, {"size": 1138, "mtime": 1753783068731, "results": "84", "hashOfConfig": "44"}, {"size": 2237, "mtime": 1753783083414, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1eejxl", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/AuthDialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/LoginForm.tsx", ["212"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/SignUpForm.tsx", ["213"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/auth/UserMenu.tsx", ["214"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundLibrary.tsx", ["215", "216", "217"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/BackgroundPanel.tsx", ["218", "219"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Canvas.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Editor.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/LayerPanel.tsx", ["220", "221", "222", "223"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectLibrary.tsx", ["224", "225"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/ObjectPanel.tsx", ["226", "227"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPanel.tsx", ["228", "229", "230", "231"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/TextPresets.tsx", ["232"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/editor/Toolbar.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/export/ExportDialog.tsx", ["233", "234", "235", "236"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SaveTemplateDialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/SubmitTemplateDialog.tsx", ["237", "238"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/template/TemplateLibrary.tsx", ["239", "240", "241", "242"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/dropdown-menu.tsx", ["243", "244"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/input.tsx", ["245"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/select.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/slider.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/components/ui/textarea.tsx", ["246"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/hooks/useEditor.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/database.ts", ["247", "248", "249", "250"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/exportService.ts", ["251", "252"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase-server.ts", ["253"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/supabase.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/templateService.ts", ["254", "255"], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/github/bobo52310/memecreater/app/src/types/index.ts", [], [], {"ruleId": "256", "severity": 2, "message": "257", "line": 33, "column": 19, "nodeType": "258", "messageId": "259", "endLine": 33, "endColumn": 22, "suggestions": "260"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 58, "column": 19, "nodeType": "258", "messageId": "259", "endLine": 58, "endColumn": 22, "suggestions": "261"}, {"ruleId": "262", "severity": 1, "message": "263", "line": 94, "column": 13, "nodeType": "264", "endLine": 94, "endColumn": 47}, {"ruleId": "265", "severity": 1, "message": "266", "line": 5, "column": 10, "nodeType": null, "messageId": "267", "endLine": 5, "endColumn": 16}, {"ruleId": "265", "severity": 1, "message": "268", "line": 10, "column": 10, "nodeType": null, "messageId": "267", "endLine": 10, "endColumn": 22}, {"ruleId": "269", "severity": 1, "message": "270", "line": 176, "column": 17, "nodeType": "264", "endLine": 180, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "271", "line": 14, "column": 12, "nodeType": null, "messageId": "267", "endLine": 14, "endColumn": 21}, {"ruleId": "269", "severity": 1, "message": "270", "line": 209, "column": 19, "nodeType": "264", "endLine": 213, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "272", "line": 14, "column": 3, "nodeType": null, "messageId": "267", "endLine": 14, "endColumn": 7}, {"ruleId": "265", "severity": 1, "message": "273", "line": 15, "column": 3, "nodeType": null, "messageId": "267", "endLine": 15, "endColumn": 9}, {"ruleId": "265", "severity": 1, "message": "274", "line": 27, "column": 5, "nodeType": null, "messageId": "267", "endLine": 27, "endColumn": 20}, {"ruleId": "256", "severity": 2, "message": "257", "line": 47, "column": 32, "nodeType": "258", "messageId": "259", "endLine": 47, "endColumn": 35, "suggestions": "275"}, {"ruleId": "265", "severity": 1, "message": "266", "line": 5, "column": 10, "nodeType": null, "messageId": "267", "endLine": 5, "endColumn": 16}, {"ruleId": "269", "severity": 1, "message": "270", "line": 189, "column": 17, "nodeType": "264", "endLine": 193, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "276", "line": 6, "column": 10, "nodeType": null, "messageId": "267", "endLine": 6, "endColumn": 15}, {"ruleId": "256", "severity": 2, "message": "257", "line": 69, "column": 56, "nodeType": "258", "messageId": "259", "endLine": 69, "endColumn": 59, "suggestions": "277"}, {"ruleId": "265", "severity": 1, "message": "278", "line": 16, "column": 3, "nodeType": null, "messageId": "267", "endLine": 16, "endColumn": 7}, {"ruleId": "265", "severity": 1, "message": "279", "line": 17, "column": 3, "nodeType": null, "messageId": "267", "endLine": 17, "endColumn": 9}, {"ruleId": "265", "severity": 1, "message": "280", "line": 19, "column": 10, "nodeType": null, "messageId": "267", "endLine": 19, "endColumn": 12}, {"ruleId": "256", "severity": 2, "message": "257", "line": 69, "column": 54, "nodeType": "258", "messageId": "259", "endLine": 69, "endColumn": 57, "suggestions": "281"}, {"ruleId": "265", "severity": 1, "message": "282", "line": 6, "column": 10, "nodeType": null, "messageId": "267", "endLine": 6, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "283", "line": 14, "column": 3, "nodeType": null, "messageId": "267", "endLine": 14, "endColumn": 9}, {"ruleId": "256", "severity": 2, "message": "257", "line": 46, "column": 46, "nodeType": "258", "messageId": "259", "endLine": 46, "endColumn": 49, "suggestions": "284"}, {"ruleId": "265", "severity": 1, "message": "285", "line": 104, "column": 16, "nodeType": null, "messageId": "267", "endLine": 104, "endColumn": 21}, {"ruleId": "269", "severity": 1, "message": "270", "line": 264, "column": 23, "nodeType": "264", "endLine": 268, "endColumn": 25}, {"ruleId": "265", "severity": 1, "message": "286", "line": 11, "column": 10, "nodeType": null, "messageId": "267", "endLine": 11, "endColumn": 15}, {"ruleId": "265", "severity": 1, "message": "287", "line": 13, "column": 18, "nodeType": null, "messageId": "267", "endLine": 13, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "280", "line": 21, "column": 10, "nodeType": null, "messageId": "267", "endLine": 21, "endColumn": 12}, {"ruleId": "256", "severity": 2, "message": "257", "line": 83, "column": 40, "nodeType": "258", "messageId": "259", "endLine": 83, "endColumn": 43, "suggestions": "288"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 111, "column": 46, "nodeType": "258", "messageId": "259", "endLine": 111, "endColumn": 49, "suggestions": "289"}, {"ruleId": "269", "severity": 1, "message": "270", "line": 231, "column": 19, "nodeType": "264", "endLine": 235, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "290", "line": 3, "column": 10, "nodeType": null, "messageId": "267", "endLine": 3, "endColumn": 15}, {"ruleId": "265", "severity": 1, "message": "291", "line": 3, "column": 31, "nodeType": null, "messageId": "267", "endLine": 3, "endColumn": 37}, {"ruleId": "292", "severity": 2, "message": "293", "line": 4, "column": 18, "nodeType": "294", "messageId": "295", "endLine": 4, "endColumn": 28, "suggestions": "296"}, {"ruleId": "292", "severity": 2, "message": "293", "line": 4, "column": 18, "nodeType": "294", "messageId": "295", "endLine": 4, "endColumn": 31, "suggestions": "297"}, {"ruleId": "265", "severity": 1, "message": "298", "line": 1, "column": 39, "nodeType": null, "messageId": "267", "endLine": 1, "endColumn": 66}, {"ruleId": "265", "severity": 1, "message": "299", "line": 2, "column": 33, "nodeType": null, "messageId": "267", "endLine": 2, "endColumn": 39}, {"ruleId": "265", "severity": 1, "message": "300", "line": 2, "column": 41, "nodeType": null, "messageId": "267", "endLine": 2, "endColumn": 48}, {"ruleId": "265", "severity": 1, "message": "301", "line": 2, "column": 50, "nodeType": null, "messageId": "267", "endLine": 2, "endColumn": 58}, {"ruleId": "256", "severity": 2, "message": "257", "line": 110, "column": 62, "nodeType": "258", "messageId": "259", "endLine": 110, "endColumn": 65, "suggestions": "302"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 127, "column": 55, "nodeType": "258", "messageId": "259", "endLine": 127, "endColumn": 58, "suggestions": "303"}, {"ruleId": "304", "severity": 2, "message": "305", "line": 49, "column": 7, "nodeType": "294", "messageId": "306", "endLine": 49, "endColumn": 15, "fix": "307"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 259, "column": 29, "nodeType": "258", "messageId": "259", "endLine": 259, "endColumn": 32, "suggestions": "308"}, {"ruleId": "256", "severity": 2, "message": "257", "line": 283, "column": 29, "nodeType": "258", "messageId": "259", "endLine": 283, "endColumn": 32, "suggestions": "309"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["310", "311"], ["312", "313"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "unusedVar", "'assetService' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'ImageIcon' is defined but never used.", "'Lock' is defined but never used.", "'Unlock' is defined but never used.", "'setLayerOpacity' is assigned a value but never used.", ["314", "315"], "'Input' is defined but never used.", ["316", "317"], "'Bold' is defined but never used.", "'Italic' is defined but never used.", "'cn' is defined but never used.", ["318", "319"], "'TextLayer' is defined but never used.", "'Share2' is defined but never used.", ["320", "321"], "'error' is defined but never used.", "'Badge' is defined but never used.", "'X' is defined but never used.", ["322", "323"], ["324", "325"], "'Check' is defined but never used.", "'Circle' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["326"], ["327"], "'createServerComponentClient' is defined but never used.", "'Rating' is defined but never used.", "'Comment' is defined but never used.", "'Favorite' is defined but never used.", ["328", "329"], ["330", "331"], "prefer-const", "'response' is never reassigned. Use 'const' instead.", "useConst", {"range": "332", "text": "333"}, ["334", "335"], ["336", "337"], {"messageId": "338", "fix": "339", "desc": "340"}, {"messageId": "341", "fix": "342", "desc": "343"}, {"messageId": "338", "fix": "344", "desc": "340"}, {"messageId": "341", "fix": "345", "desc": "343"}, {"messageId": "338", "fix": "346", "desc": "340"}, {"messageId": "341", "fix": "347", "desc": "343"}, {"messageId": "338", "fix": "348", "desc": "340"}, {"messageId": "341", "fix": "349", "desc": "343"}, {"messageId": "338", "fix": "350", "desc": "340"}, {"messageId": "341", "fix": "351", "desc": "343"}, {"messageId": "338", "fix": "352", "desc": "340"}, {"messageId": "341", "fix": "353", "desc": "343"}, {"messageId": "338", "fix": "354", "desc": "340"}, {"messageId": "341", "fix": "355", "desc": "343"}, {"messageId": "338", "fix": "356", "desc": "340"}, {"messageId": "341", "fix": "357", "desc": "343"}, {"messageId": "358", "fix": "359", "desc": "360"}, {"messageId": "358", "fix": "361", "desc": "360"}, {"messageId": "338", "fix": "362", "desc": "340"}, {"messageId": "341", "fix": "363", "desc": "343"}, {"messageId": "338", "fix": "364", "desc": "340"}, {"messageId": "341", "fix": "365", "desc": "343"}, [1506, 1599], "const response = NextResponse.next({\n    request: {\n      headers: request.headers,\n    },\n  })", {"messageId": "338", "fix": "366", "desc": "340"}, {"messageId": "341", "fix": "367", "desc": "343"}, {"messageId": "338", "fix": "368", "desc": "340"}, {"messageId": "341", "fix": "369", "desc": "343"}, "suggestUnknown", {"range": "370", "text": "371"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "372", "text": "373"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "374", "text": "371"}, {"range": "375", "text": "373"}, {"range": "376", "text": "371"}, {"range": "377", "text": "373"}, {"range": "378", "text": "371"}, {"range": "379", "text": "373"}, {"range": "380", "text": "371"}, {"range": "381", "text": "373"}, {"range": "382", "text": "371"}, {"range": "383", "text": "373"}, {"range": "384", "text": "371"}, {"range": "385", "text": "373"}, {"range": "386", "text": "371"}, {"range": "387", "text": "373"}, "replaceEmptyInterfaceWithSuper", {"range": "388", "text": "389"}, "Replace empty interface with a type alias.", {"range": "390", "text": "391"}, {"range": "392", "text": "371"}, {"range": "393", "text": "373"}, {"range": "394", "text": "371"}, {"range": "395", "text": "373"}, {"range": "396", "text": "371"}, {"range": "397", "text": "373"}, {"range": "398", "text": "371"}, {"range": "399", "text": "373"}, [998, 1001], "unknown", [998, 1001], "never", [1626, 1629], [1626, 1629], [1228, 1231], [1228, 1231], [1999, 2002], [1999, 2002], [1966, 1969], [1966, 1969], [1652, 1655], [1652, 1655], [2431, 2434], [2431, 2434], [3032, 3035], [3032, 3035], [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [72, 158], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [2707, 2710], [2707, 2710], [3140, 3143], [3140, 3143], [6064, 6067], [6064, 6067], [6664, 6667], [6664, 6667]]