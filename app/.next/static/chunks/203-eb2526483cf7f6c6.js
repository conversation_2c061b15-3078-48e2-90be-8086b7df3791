"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[203],{29:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Pixelate=void 0;let n=r(8949),i=r(6509),a=r(913),o=r(6394);t.Pixelate=function(e){let t=Math.ceil(this.pixelSize()),r=e.width,n=e.height,a=Math.ceil(r/t),o=Math.ceil(n/t),s=e.data;if(t<=0)return void i.Util.error("pixelSize value can not be <= 0");for(let e=0;e<a;e+=1)for(let i=0;i<o;i+=1){let a=0,o=0,l=0,d=0,h=e*t,c=h+t,u=i*t,f=u+t,p=0;for(let e=h;e<c;e+=1)if(!(e>=r))for(let t=u;t<f;t+=1){if(t>=n)continue;let i=(r*t+e)*4;a+=s[i+0],o+=s[i+1],l+=s[i+2],d+=s[i+3],p+=1}a/=p,o/=p,l/=p,d/=p;for(let e=h;e<c;e+=1)if(!(e>=r))for(let t=u;t<f;t+=1){if(t>=n)continue;let i=(r*t+e)*4;s[i+0]=a,s[i+1]=o,s[i+2]=l,s[i+3]=d}}},n.Factory.addGetterSetter(a.Node,"pixelSize",8,(0,o.getNumberValidator)(),n.Factory.afterSetFilter)},48:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Kaleidoscope=void 0;let n=r(8949),i=r(913),a=r(6509),o=r(6394),s=function(e,t,r){let n=e.data,i=t.data,a=e.width,o=e.height,s=r.polarCenterX||a/2,l=r.polarCenterY||o/2,d=Math.sqrt(s*s+l*l),h=a-s,c=o-l,u=Math.sqrt(h*h+c*c);d=u>d?u:d;let f=360/a*Math.PI/180;for(let e=0;e<a;e+=1){let t=Math.sin(e*f),r=Math.cos(e*f);for(let u=0;u<o;u+=1){h=Math.floor(s+d*u/o*r);let f=((c=Math.floor(l+d*u/o*t))*a+h)*4,p=n[f+0],g=n[f+1],m=n[f+2],v=n[f+3];i[(f=(e+u*a)*4)+0]=p,i[f+1]=g,i[f+2]=m,i[f+3]=v}}},l=function(e,t,r){let n,i=e.data,a=t.data,o=e.width,s=e.height,l=r.polarCenterX||o/2,d=r.polarCenterY||s/2,h=Math.sqrt(l*l+d*d),c=o-l,u=s-d,f=Math.sqrt(c*c+u*u);h=f>h?f:h;let p=r.polarRotation||0;for(c=0;c<o;c+=1)for(u=0;u<s;u+=1){let e=c-l,t=u-d,r=Math.sqrt(e*e+t*t)*s/h,f=(180*Math.atan2(t,e)/Math.PI+360+p)%360;n=Math.floor(f=f*o/360);let g=(Math.floor(r)*o+n)*4,m=i[g+0],v=i[g+1],y=i[g+2],b=i[g+3];a[(g=(u*o+c)*4)+0]=m,a[g+1]=v,a[g+2]=y,a[g+3]=b}};t.Kaleidoscope=function(e){let t,r,n,i,o,d,h,c,u,f,p=e.width,g=e.height,m=Math.round(this.kaleidoscopePower()),v=Math.floor(Math.round(this.kaleidoscopeAngle())%360*p/360);if(m<1)return;let y=a.Util.createCanvasElement();y.width=p,y.height=g;let b=y.getContext("2d").getImageData(0,0,p,g);a.Util.releaseCanvas(y),s(e,b,{polarCenterX:p/2,polarCenterY:g/2});let x=p/Math.pow(2,m);for(;x<=8;)x*=2,m-=1;let w=x=Math.ceil(x),_=0,S=w,C=1;for(v+x>p&&(_=w,S=0,C=-1),r=0;r<g;r+=1)for(t=_;t!==S;t+=C)n=Math.round(t+v)%p,u=(p*r+n)*4,o=b.data[u+0],d=b.data[u+1],h=b.data[u+2],c=b.data[u+3],f=(p*r+t)*4,b.data[f+0]=o,b.data[f+1]=d,b.data[f+2]=h,b.data[f+3]=c;for(r=0;r<g;r+=1)for(i=0,w=Math.floor(x);i<m;i+=1){for(t=0;t<w+1;t+=1)u=(p*r+t)*4,o=b.data[u+0],d=b.data[u+1],h=b.data[u+2],c=b.data[u+3],f=(p*r+2*w-t-1)*4,b.data[f+0]=o,b.data[f+1]=d,b.data[f+2]=h,b.data[f+3]=c;w*=2}l(b,e,{polarRotation:0})},n.Factory.addGetterSetter(i.Node,"kaleidoscopePower",2,(0,o.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"kaleidoscopeAngle",0,(0,o.getNumberValidator)(),n.Factory.afterSetFilter)},165:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DD=void 0;let n=r(6878),i=r(6509);t.DD={get isDragging(){let e=!1;return t.DD._dragElements.forEach(t=>{"dragging"===t.dragStatus&&(e=!0)}),e},justDragged:!1,get node(){let e;return t.DD._dragElements.forEach(t=>{e=t.node}),e},_dragElements:new Map,_drag(e){let r=[];t.DD._dragElements.forEach((t,n)=>{let{node:a}=t,o=a.getStage();o.setPointersPositions(e),void 0===t.pointerId&&(t.pointerId=i.Util._getFirstPointerId(e));let s=o._changedPointerPositions.find(e=>e.id===t.pointerId);if(s){if("dragging"!==t.dragStatus){let r=a.dragDistance();if(Math.max(Math.abs(s.x-t.startPointerPos.x),Math.abs(s.y-t.startPointerPos.y))<r||(a.startDrag({evt:e}),!a.isDragging()))return}a._setDragPosition(e,t),r.push(a)}}),r.forEach(t=>{t.fire("dragmove",{type:"dragmove",target:t,evt:e},!0)})},_endDragBefore(e){let r=[];t.DD._dragElements.forEach(i=>{let{node:a}=i,o=a.getStage();if(e&&o.setPointersPositions(e),!o._changedPointerPositions.find(e=>e.id===i.pointerId))return;("dragging"===i.dragStatus||"stopped"===i.dragStatus)&&(t.DD.justDragged=!0,n.Konva._mouseListenClick=!1,n.Konva._touchListenClick=!1,n.Konva._pointerListenClick=!1,i.dragStatus="stopped");let s=i.node.getLayer()||i.node instanceof n.Konva.Stage&&i.node;s&&-1===r.indexOf(s)&&r.push(s)}),r.forEach(e=>{e.draw()})},_endDragAfter(e){t.DD._dragElements.forEach((r,n)=>{"stopped"===r.dragStatus&&r.node.fire("dragend",{type:"dragend",target:r.node,evt:e},!0),"dragging"!==r.dragStatus&&t.DD._dragElements.delete(n)})}},n.Konva.isBrowser&&(window.addEventListener("mouseup",t.DD._endDragBefore,!0),window.addEventListener("touchend",t.DD._endDragBefore,!0),window.addEventListener("touchcancel",t.DD._endDragBefore,!0),window.addEventListener("mousemove",t.DD._drag),window.addEventListener("touchmove",t.DD._drag),window.addEventListener("mouseup",t.DD._endDragAfter,!1),window.addEventListener("touchend",t.DD._endDragAfter,!1),window.addEventListener("touchcancel",t.DD._endDragAfter,!1))},381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},488:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},556:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},704:(e,t,r)=>{r.d(t,{B8:()=>T,UC:()=>R,bL:()=>M,l9:()=>E});var n=r(2115),i=r(5185),a=r(6081),o=r(9196),s=r(8905),l=r(3655),d=r(4315),h=r(5845),c=r(1285),u=r(5155),f="Tabs",[p,g]=(0,a.A)(f,[o.RG]),m=(0,o.RG)(),[v,y]=p(f),b=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:a,orientation:o="horizontal",dir:s,activationMode:p="automatic",...g}=e,m=(0,d.jH)(s),[y,b]=(0,h.i)({prop:n,onChange:i,defaultProp:null!=a?a:"",caller:f});return(0,u.jsx)(v,{scope:r,baseId:(0,c.B)(),value:y,onValueChange:b,orientation:o,dir:m,activationMode:p,children:(0,u.jsx)(l.sG.div,{dir:m,"data-orientation":o,...g,ref:t})})});b.displayName=f;var x="TabsList",w=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,a=y(x,r),s=m(r);return(0,u.jsx)(o.bL,{asChild:!0,...s,orientation:a.orientation,dir:a.dir,loop:n,children:(0,u.jsx)(l.sG.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});w.displayName=x;var _="TabsTrigger",S=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...s}=e,d=y(_,r),h=m(r),c=P(d.baseId,n),f=A(d.baseId,n),p=n===d.value;return(0,u.jsx)(o.q7,{asChild:!0,...h,focusable:!a,active:p,children:(0,u.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...s,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||a||!e||d.onValueChange(n)})})})});S.displayName=_;var C="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:a,children:o,...d}=e,h=y(C,r),c=P(h.baseId,i),f=A(h.baseId,i),p=i===h.value,g=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(s.C,{present:a||p,children:r=>{let{present:n}=r;return(0,u.jsx)(l.sG.div,{"data-state":p?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:n&&o})}})});function P(e,t){return"".concat(e,"-trigger-").concat(t)}function A(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=C;var M=b,T=w,E=S,R=k},748:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Solarize=void 0,t.Solarize=function(e){let t=e.data,r=e.width,n=e.height,i=4*r,a=n;do{let e=(a-1)*i,n=r;do{let r=e+(n-1)*4,i=t[r],a=t[r+1],o=t[r+2];i>127&&(i=255-i),a>127&&(a=255-a),o>127&&(o=255-o),t[r]=i,t[r+1]=a,t[r+2]=o}while(--n)}while(--a)}},773:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Stage=t.stages=void 0;let n=r(6509),i=r(8949),a=r(1248),o=r(6878),s=r(4421),l=r(165),d=r(6878),h=r(4555),c="mouseleave",u="mouseover",f="mouseenter",p="mousemove",g="mousedown",m="mouseup",v="pointermove",y="pointerdown",b="pointerup",x="pointercancel",w="pointerout",_="pointerleave",S="pointerover",C="pointerenter",k="contextmenu",P="touchstart",A="touchend",M="touchmove",T="touchcancel",E="wheel",R=[[f,"_pointerenter"],[g,"_pointerdown"],[p,"_pointermove"],[m,"_pointerup"],[c,"_pointerleave"],[P,"_pointerdown"],[M,"_pointermove"],[A,"_pointerup"],[T,"_pointercancel"],[u,"_pointerover"],[E,"_wheel"],[k,"_contextmenu"],[y,"_pointerdown"],[v,"_pointermove"],[b,"_pointerup"],[x,"_pointercancel"],[_,"_pointerleave"],["lostpointercapture","_lostpointercapture"]],D={mouse:{[w]:"mouseout",[_]:c,[S]:u,[C]:f,[v]:p,[y]:g,[b]:m,[x]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[w]:"touchout",[_]:"touchleave",[S]:"touchover",[C]:"touchenter",[v]:M,[y]:P,[b]:A,[x]:T,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[w]:w,[_]:_,[S]:S,[C]:C,[v]:v,[y]:y,[b]:b,[x]:x,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},N=e=>e.indexOf("pointer")>=0?"pointer":e.indexOf("touch")>=0?"touch":"mouse",F=e=>{let t=N(e);return"pointer"===t?o.Konva.pointerEventsEnabled&&D.pointer:"touch"===t?D.touch:"mouse"===t?D.mouse:void 0};function O(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(e.clipFunc||e.clipWidth||e.clipHeight)&&n.Util.warn("Stage does not support clipping. Please use clip for Layers or Groups."),e}t.stages=[];class L extends a.Container{_validateAdd(e){let t="Layer"===e.getType(),r="FastLayer"===e.getType();t||r||n.Util.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;let e=this.visible()?"":"none";this.content.style.display=e}setContainer(e){if("string"==typeof e){let t;if("."===e.charAt(0)){let t=e.slice(1);e=document.getElementsByClassName(t)[0]}else t="#"!==e.charAt(0)?e:e.slice(1),e=document.getElementById(t);if(!e)throw"Can not find container in document with id "+t}return this._setAttr("container",e),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),e.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){let e=this.children,t=e.length;for(let r=0;r<t;r++)e[r].clear();return this}clone(e){return e||(e={}),e.container="undefined"!=typeof document&&document.createElement("div"),a.Container.prototype.clone.call(this,e)}destroy(){super.destroy();let e=this.content;e&&n.Util._isInDocument(e)&&this.container().removeChild(e);let r=t.stages.indexOf(this);return r>-1&&t.stages.splice(r,1),n.Util.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){let e=this._pointerPositions[0]||this._changedPointerPositions[0];return e?{x:e.x,y:e.y}:(n.Util.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)}_getPointerById(e){return this._pointerPositions.find(t=>t.id===e)}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(e){(e=e||{}).x=e.x||0,e.y=e.y||0,e.width=e.width||this.width(),e.height=e.height||this.height();let t=new s.SceneCanvas({width:e.width,height:e.height,pixelRatio:e.pixelRatio||1}),r=t.getContext()._context,n=this.children;return(e.x||e.y)&&r.translate(-1*e.x,-1*e.y),n.forEach(function(t){if(!t.isVisible())return;let n=t._toKonvaCanvas(e);r.drawImage(n._canvas,e.x,e.y,n.getWidth()/n.getPixelRatio(),n.getHeight()/n.getPixelRatio())}),t}getIntersection(e){if(!e)return null;let t=this.children,r=t.length-1;for(let n=r;n>=0;n--){let r=t[n].getIntersection(e);if(r)return r}return null}_resizeDOM(){let e=this.width(),t=this.height();this.content&&(this.content.style.width=e+"px",this.content.style.height=t+"px"),this.bufferCanvas.setSize(e,t),this.bufferHitCanvas.setSize(e,t),this.children.forEach(r=>{r.setSize({width:e,height:t}),r.draw()})}add(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];if(arguments.length>1){for(let e=0;e<arguments.length;e++)this.add(arguments[e]);return this}super.add(e);let a=this.children.length;return a>5&&n.Util.warn("The stage has "+a+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),e.setSize({width:this.width(),height:this.height()}),e.draw(),o.Konva.isBrowser&&this.content.appendChild(e.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(e){return h.hasPointerCapture(e,this)}setPointerCapture(e){h.setPointerCapture(e,this)}releaseCapture(e){h.releaseCapture(e,this)}getLayers(){return this.children}_bindContentEvents(){o.Konva.isBrowser&&R.forEach(e=>{let[t,r]=e;this.content.addEventListener(t,e=>{this[r](e)},{passive:!1})})}_pointerenter(e){this.setPointersPositions(e);let t=F(e.type);t&&this._fire(t.pointerenter,{evt:e,target:this,currentTarget:this})}_pointerover(e){this.setPointersPositions(e);let t=F(e.type);t&&this._fire(t.pointerover,{evt:e,target:this,currentTarget:this})}_getTargetShape(e){let t=this[e+"targetShape"];return t&&!t.getStage()&&(t=null),t}_pointerleave(e){let t=F(e.type),r=N(e.type);if(!t)return;this.setPointersPositions(e);let n=this._getTargetShape(r),i=!(o.Konva.isDragging()||o.Konva.isTransforming())||o.Konva.hitOnDragEnabled;n&&i?(n._fireAndBubble(t.pointerout,{evt:e}),n._fireAndBubble(t.pointerleave,{evt:e}),this._fire(t.pointerleave,{evt:e,target:this,currentTarget:this}),this[r+"targetShape"]=null):i&&(this._fire(t.pointerleave,{evt:e,target:this,currentTarget:this}),this._fire(t.pointerout,{evt:e,target:this,currentTarget:this})),this.pointerPos=null,this._pointerPositions=[]}_pointerdown(e){let t=F(e.type),r=N(e.type);if(!t)return;this.setPointersPositions(e);let n=!1;this._changedPointerPositions.forEach(i=>{let a=this.getIntersection(i);if(l.DD.justDragged=!1,o.Konva["_"+r+"ListenClick"]=!0,!a||!a.isListening()){this[r+"ClickStartShape"]=void 0;return}o.Konva.capturePointerEventsEnabled&&a.setPointerCapture(i.id),this[r+"ClickStartShape"]=a,a._fireAndBubble(t.pointerdown,{evt:e,pointerId:i.id}),n=!0;let s=e.type.indexOf("touch")>=0;a.preventDefault()&&e.cancelable&&s&&e.preventDefault()}),n||this._fire(t.pointerdown,{evt:e,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}_pointermove(e){let t=F(e.type),r=N(e.type);if(!t||(o.Konva.isDragging()&&l.DD.node.preventDefault()&&e.cancelable&&e.preventDefault(),this.setPointersPositions(e),!(!(o.Konva.isDragging()||o.Konva.isTransforming())||o.Konva.hitOnDragEnabled)))return;let n={},i=!1,a=this._getTargetShape(r);this._changedPointerPositions.forEach(o=>{let s=h.getCapturedShape(o.id)||this.getIntersection(o),l=o.id,d={evt:e,pointerId:l},c=a!==s;if(c&&a&&(a._fireAndBubble(t.pointerout,{...d},s),a._fireAndBubble(t.pointerleave,{...d},s)),s){if(n[s._id])return;n[s._id]=!0}s&&s.isListening()?(i=!0,c&&(s._fireAndBubble(t.pointerover,{...d},a),s._fireAndBubble(t.pointerenter,{...d},a),this[r+"targetShape"]=s),s._fireAndBubble(t.pointermove,{...d})):a&&(this._fire(t.pointerover,{evt:e,target:this,currentTarget:this,pointerId:l}),this[r+"targetShape"]=null)}),i||this._fire(t.pointermove,{evt:e,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(e){let t=F(e.type),r=N(e.type);if(!t)return;this.setPointersPositions(e);let n=this[r+"ClickStartShape"],i=this[r+"ClickEndShape"],a={},s=!1;this._changedPointerPositions.forEach(d=>{let c=h.getCapturedShape(d.id)||this.getIntersection(d);if(c){if(c.releaseCapture(d.id),a[c._id])return;a[c._id]=!0}let u=d.id,f={evt:e,pointerId:u},p=!1;o.Konva["_"+r+"InDblClickWindow"]?(p=!0,clearTimeout(this[r+"DblTimeout"])):l.DD.justDragged||(o.Konva["_"+r+"InDblClickWindow"]=!0,clearTimeout(this[r+"DblTimeout"])),this[r+"DblTimeout"]=setTimeout(function(){o.Konva["_"+r+"InDblClickWindow"]=!1},o.Konva.dblClickWindow),c&&c.isListening()?(s=!0,this[r+"ClickEndShape"]=c,c._fireAndBubble(t.pointerup,{...f}),o.Konva["_"+r+"ListenClick"]&&n&&n===c&&(c._fireAndBubble(t.pointerclick,{...f}),p&&i&&i===c&&c._fireAndBubble(t.pointerdblclick,{...f}))):(this[r+"ClickEndShape"]=null,o.Konva["_"+r+"ListenClick"]&&this._fire(t.pointerclick,{evt:e,target:this,currentTarget:this,pointerId:u}),p&&this._fire(t.pointerdblclick,{evt:e,target:this,currentTarget:this,pointerId:u}))}),s||this._fire(t.pointerup,{evt:e,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),o.Konva["_"+r+"ListenClick"]=!1,e.cancelable&&"touch"!==r&&"pointer"!==r&&e.preventDefault()}_contextmenu(e){this.setPointersPositions(e);let t=this.getIntersection(this.getPointerPosition());t&&t.isListening()?t._fireAndBubble(k,{evt:e}):this._fire(k,{evt:e,target:this,currentTarget:this})}_wheel(e){this.setPointersPositions(e);let t=this.getIntersection(this.getPointerPosition());t&&t.isListening()?t._fireAndBubble(E,{evt:e}):this._fire(E,{evt:e,target:this,currentTarget:this})}_pointercancel(e){this.setPointersPositions(e);let t=h.getCapturedShape(e.pointerId)||this.getIntersection(this.getPointerPosition());t&&t._fireAndBubble(b,h.createEvent(e)),h.releaseCapture(e.pointerId)}_lostpointercapture(e){h.releaseCapture(e.pointerId)}setPointersPositions(e){let t=this._getContentPosition(),r=null,i=null;void 0!==(e=e||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(e.touches,e=>{this._pointerPositions.push({id:e.identifier,x:(e.clientX-t.left)/t.scaleX,y:(e.clientY-t.top)/t.scaleY})}),Array.prototype.forEach.call(e.changedTouches||e.touches,e=>{this._changedPointerPositions.push({id:e.identifier,x:(e.clientX-t.left)/t.scaleX,y:(e.clientY-t.top)/t.scaleY})})):(r=(e.clientX-t.left)/t.scaleX,i=(e.clientY-t.top)/t.scaleY,this.pointerPos={x:r,y:i},this._pointerPositions=[{x:r,y:i,id:n.Util._getFirstPointerId(e)}],this._changedPointerPositions=[{x:r,y:i,id:n.Util._getFirstPointerId(e)}])}_setPointerPosition(e){n.Util.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(e)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};let e=this.content.getBoundingClientRect();return{top:e.top,left:e.left,scaleX:e.width/this.content.clientWidth||1,scaleY:e.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new s.SceneCanvas({width:this.width(),height:this.height()}),this.bufferHitCanvas=new s.HitCanvas({pixelRatio:1,width:this.width(),height:this.height()}),!o.Konva.isBrowser)return;let e=this.container();if(!e)throw"Stage has no container. A container is required.";e.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),e.appendChild(this.content),this._resizeDOM()}cache(){return n.Util.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach(function(e){e.batchDraw()}),this}constructor(e){super(O(e)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),t.stages.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",()=>{O(this.attrs)}),this._checkVisibility()}}t.Stage=L,L.prototype.nodeType="Stage",(0,d._registerNode)(L),i.Factory.addGetterSetter(L,"container"),o.Konva.isBrowser&&document.addEventListener("visibilitychange",()=>{t.stages.forEach(e=>{e.batchDraw()})})},801:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Enhance=void 0;let n=r(8949),i=r(913),a=r(6394);function o(e,t,r,n,i){let a=r-t,o=i-n;if(0===a)return n+o/2;if(0===o)return n;let s=(e-t)/a;return o*s+n}t.Enhance=function(e){let t,r,n,i,a,s,l=e.data,d=l.length,h=l[0],c=h,u,f=l[1],p=f,g,m=l[2],v=m,y,b=this.enhance();if(0!==b){for(let e=0;e<d;e+=4)(u=l[e+0])<h?h=u:u>c&&(c=u),(g=l[e+1])<f?f=g:g>p&&(p=g),(y=l[e+2])<m?m=y:y>v&&(v=y);if(c===h&&(c=255,h=0),p===f&&(p=255,f=0),v===m&&(v=255,m=0),b>0)t=c+b*(255-c),r=h-b*(h-0),n=p+b*(255-p),i=f-b*(f-0),a=v+b*(255-v),s=m-b*(m-0);else{let e=(c+h)*.5;t=c+b*(c-e),r=h+b*(h-e);let o=(p+f)*.5;n=p+b*(p-o),i=f+b*(f-o);let l=(v+m)*.5;a=v+b*(v-l),s=m+b*(m-l)}for(let e=0;e<d;e+=4)l[e+0]=o(l[e+0],h,c,r,t),l[e+1]=o(l[e+1],f,p,i,n),l[e+2]=o(l[e+2],m,v,s,a)}},n.Factory.addGetterSetter(i.Node,"enhance",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},836:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Group=void 0;let n=r(6509),i=r(1248),a=r(6878);class o extends i.Container{_validateAdd(e){let t=e.getType();"Group"!==t&&"Shape"!==t&&n.Util.throw("You may only add groups and shapes to groups.")}}t.Group=o,o.prototype.nodeType="Group",(0,a._registerNode)(o)},913:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Node=void 0;let n=r(4421),i=r(165),a=r(8949),o=r(6878),s=r(6509),l=r(6394),d="absoluteOpacity",h="allEventListeners",c="absoluteTransform",u="absoluteScale",f="canvas",p="listening",g="Shape",m="stage",v="transform",y="visible",b=1;class x{constructor(e){this._id=b++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(e),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(e){(e===v||e===c)&&this._cache.get(e)?this._cache.get(e).dirty=!0:e?this._cache.delete(e):this._cache.clear()}_getCache(e,t){let r=this._cache.get(e),n=e===v||e===c;return(void 0===r||n&&!0===r.dirty)&&(r=t.call(this),this._cache.set(e,r)),r}_calculate(e,t,r){if(!this._attachedDepsListeners.get(e)){let r=t.map(e=>e+"Change.konva").join(" ");this.on(r,()=>{this._clearCache(e)}),this._attachedDepsListeners.set(e,!0)}return this._getCache(e,r)}_getCanvasCache(){return this._cache.get(f)}_clearSelfAndDescendantCache(e){this._clearCache(e),e===c&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(f)){let{scene:e,filter:t,hit:r,buffer:n}=this._cache.get(f);s.Util.releaseCanvas(e,t,r,n),this._cache.delete(f)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(e){let t=e||{},r={};(void 0===t.x||void 0===t.y||void 0===t.width||void 0===t.height)&&(r=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()||void 0}));let i=Math.ceil(t.width||r.width),a=Math.ceil(t.height||r.height),o=t.pixelRatio,l=void 0===t.x?Math.floor(r.x):t.x,h=void 0===t.y?Math.floor(r.y):t.y,c=t.offset||0,p=t.drawBorder||!1,g=t.hitCanvasPixelRatio||1;if(!i||!a)return void s.Util.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");let m=+(Math.abs(Math.round(r.x)-l)>.5),v=+(Math.abs(Math.round(r.y)-h)>.5);i+=2*c+m,a+=2*c+v,l-=c,h-=c;let y=new n.SceneCanvas({pixelRatio:o,width:i,height:a}),b=new n.SceneCanvas({pixelRatio:o,width:0,height:0,willReadFrequently:!0}),x=new n.HitCanvas({pixelRatio:g,width:i,height:a}),w=y.getContext(),_=x.getContext(),S=new n.SceneCanvas({width:y.width/y.pixelRatio+Math.abs(l),height:y.height/y.pixelRatio+Math.abs(h),pixelRatio:y.pixelRatio}),C=S.getContext();return x.isCache=!0,y.isCache=!0,this._cache.delete(f),this._filterUpToDate=!1,!1===t.imageSmoothingEnabled&&(y.getContext()._context.imageSmoothingEnabled=!1,b.getContext()._context.imageSmoothingEnabled=!1),w.save(),_.save(),C.save(),w.translate(-l,-h),_.translate(-l,-h),C.translate(-l,-h),S.x=l,S.y=h,this._isUnderCache=!0,this._clearSelfAndDescendantCache(d),this._clearSelfAndDescendantCache(u),this.drawScene(y,this,S),this.drawHit(x,this),this._isUnderCache=!1,w.restore(),_.restore(),p&&(w.save(),w.beginPath(),w.rect(0,0,i,a),w.closePath(),w.setAttr("strokeStyle","red"),w.setAttr("lineWidth",5),w.stroke(),w.restore()),this._cache.set(f,{scene:y,filter:b,hit:x,buffer:S,x:l,y:h}),this._requestDraw(),this}isCached(){return this._cache.has(f)}getClientRect(e){throw Error('abstract "getClientRect" method call')}_transformedRect(e,t){let r=[{x:e.x,y:e.y},{x:e.x+e.width,y:e.y},{x:e.x+e.width,y:e.y+e.height},{x:e.x,y:e.y+e.height}],n=1/0,i=1/0,a=-1/0,o=-1/0,s=this.getAbsoluteTransform(t);return r.forEach(function(e){let t=s.point(e);void 0===n&&(n=a=t.x,i=o=t.y),n=Math.min(n,t.x),i=Math.min(i,t.y),a=Math.max(a,t.x),o=Math.max(o,t.y)}),{x:n,y:i,width:a-n,height:o-i}}_drawCachedSceneCanvas(e){e.save(),e._applyOpacity(this),e._applyGlobalCompositeOperation(this);let t=this._getCanvasCache();e.translate(t.x,t.y);let r=this._getCachedSceneCanvas(),n=r.pixelRatio;e.drawImage(r._canvas,0,0,r.width/n,r.height/n),e.restore()}_drawCachedHitCanvas(e){let t=this._getCanvasCache(),r=t.hit;e.save(),e.translate(t.x,t.y),e.drawImage(r._canvas,0,0,r.width/r.pixelRatio,r.height/r.pixelRatio),e.restore()}_getCachedSceneCanvas(){let e=this.filters(),t=this._getCanvasCache(),r=t.scene,n=t.filter,i=n.getContext(),a,o,l,d;if(e){if(!this._filterUpToDate){let t=r.pixelRatio;n.setSize(r.width/r.pixelRatio,r.height/r.pixelRatio);try{for(a=e.length,i.clear(),i.drawImage(r._canvas,0,0,r.getWidth()/t,r.getHeight()/t),o=i.getImageData(0,0,n.getWidth(),n.getHeight()),l=0;l<a;l++){if(d=e[l],"function"!=typeof d){s.Util.error("Filter should be type of function, but got "+typeof d+" instead. Please check correct filters");continue}d.call(this,o),i.putImageData(o,0,0)}}catch(e){s.Util.error("Unable to apply filter. "+e.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return n}return r}on(e,t){if(this._cache&&this._cache.delete(h),3==arguments.length)return this._delegate.apply(this,arguments);let r=e.split(" ");for(let e=0;e<r.length;e++){let n=r[e].split("."),i=n[0],a=n[1]||"";this.eventListeners[i]||(this.eventListeners[i]=[]),this.eventListeners[i].push({name:a,handler:t})}return this}off(e,t){let r=(e||"").split(" "),n=r.length,i,a,o,s,l;if(this._cache&&this._cache.delete(h),!e)for(a in this.eventListeners)this._off(a);for(i=0;i<n;i++)if(s=(o=r[i].split("."))[0],l=o[1],s)this.eventListeners[s]&&this._off(s,l,t);else for(a in this.eventListeners)this._off(a,l,t);return this}dispatchEvent(e){let t={target:this,type:e.type,evt:e};return this.fire(e.type,t),this}addEventListener(e,t){return this.on(e,function(e){t.call(this,e.evt)}),this}removeEventListener(e){return this.off(e),this}_delegate(e,t,r){let n=this;this.on(e,function(e){let i=e.target.findAncestors(t,!0,n);for(let t=0;t<i.length;t++)(e=s.Util.cloneObject(e)).currentTarget=i[t],r.call(i[t],e)})}remove(){return this.isDragging()&&this.stopDrag(),i.DD._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(c),this._clearSelfAndDescendantCache(d),this._clearSelfAndDescendantCache(u),this._clearSelfAndDescendantCache(m),this._clearSelfAndDescendantCache(y),this._clearSelfAndDescendantCache(p)}_remove(){this._clearCaches();let e=this.getParent();e&&e.children&&(e.children.splice(this.index,1),e._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(e){let t="get"+s.Util._capitalize(e);return s.Util._isFunction(this[t])?this[t]():this.attrs[e]}getAncestors(){let e=this.getParent(),t=[];for(;e;)t.push(e),e=e.getParent();return t}getAttrs(){return this.attrs||{}}setAttrs(e){return this._batchTransformChanges(()=>{let t,r;if(!e)return this;for(t in e)"children"!==t&&(r="set"+s.Util._capitalize(t),s.Util._isFunction(this[r])?this[r](e[t]):this._setAttr(t,e[t]))}),this}isListening(){return this._getCache(p,this._isListening)}_isListening(e){if(!this.listening())return!1;let t=this.getParent();return!t||t===e||this===e||t._isListening(e)}isVisible(){return this._getCache(y,this._isVisible)}_isVisible(e){if(!this.visible())return!1;let t=this.getParent();return!t||t===e||this===e||t._isVisible(e)}shouldDrawHit(e,t=!1){if(e)return this._isVisible(e)&&this._isListening(e);let r=this.getLayer(),n=!1;i.DD._dragElements.forEach(e=>{"dragging"===e.dragStatus&&("Stage"===e.node.nodeType?n=!0:e.node.getLayer()===r&&(n=!0))});let a=!t&&!o.Konva.hitOnDragEnabled&&(n||o.Konva.isTransforming());return this.isListening()&&this.isVisible()&&!a}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){let e=this.getDepth(),t=this,r=0,n,i,a,o,s=this.getStage();return"Stage"!==t.nodeType&&s&&function s(l){for(a=0,n=[],i=l.length;a<i;a++)o=l[a],r++,o.nodeType!==g&&(n=n.concat(o.getChildren().slice())),o._id===t._id&&(a=i);n.length>0&&n[0].getDepth()<=e&&s(n)}(s.getChildren()),r}getDepth(){let e=0,t=this.parent;for(;t;)e++,t=t.parent;return e}_batchTransformChanges(e){this._batchingTransformChange=!0,e(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(v),this._clearSelfAndDescendantCache(c)),this._needClearTransformCache=!1}setPosition(e){return this._batchTransformChanges(()=>{this.x(e.x),this.y(e.y)}),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){let e=this.getStage();if(!e)return null;let t=e.getPointerPosition();if(!t)return null;let r=this.getAbsoluteTransform().copy();return r.invert(),r.point(t)}getAbsolutePosition(e){let t=!1,r=this.parent;for(;r;){if(r.isCached()){t=!0;break}r=r.parent}t&&!e&&(e=!0);let n=this.getAbsoluteTransform(e).getMatrix(),i=new s.Transform,a=this.offset();return i.m=n.slice(),i.translate(a.x,a.y),i.getTranslation()}setAbsolutePosition(e){let{x:t,y:r,...n}=this._clearTransform();this.attrs.x=t,this.attrs.y=r,this._clearCache(v);let i=this._getAbsoluteTransform().copy();return i.invert(),i.translate(e.x,e.y),e={x:this.attrs.x+i.getTranslation().x,y:this.attrs.y+i.getTranslation().y},this._setTransform(n),this.setPosition({x:e.x,y:e.y}),this._clearCache(v),this._clearSelfAndDescendantCache(c),this}_setTransform(e){let t;for(t in e)this.attrs[t]=e[t]}_clearTransform(){let e={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,e}move(e){let t=e.x,r=e.y,n=this.x(),i=this.y();return void 0!==t&&(n+=t),void 0!==r&&(i+=r),this.setPosition({x:n,y:i}),this}_eachAncestorReverse(e,t){let r=[],n=this.getParent(),i,a;if(!t||t._id!==this._id){for(r.unshift(this);n&&(!t||n._id!==t._id);)r.unshift(n),n=n.parent;for(a=0,i=r.length;a<i;a++)e(r[a])}}rotate(e){return this.rotation(this.rotation()+e),this}moveToTop(){if(!this.parent)return s.Util.warn("Node has no parent. moveToTop function is ignored."),!1;let e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0)}moveUp(){if(!this.parent)return s.Util.warn("Node has no parent. moveUp function is ignored."),!1;let e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.splice(e+1,0,this),this.parent._setChildrenIndices(),!0)}moveDown(){if(!this.parent)return s.Util.warn("Node has no parent. moveDown function is ignored."),!1;let e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.splice(e-1,0,this),this.parent._setChildrenIndices(),!0)}moveToBottom(){if(!this.parent)return s.Util.warn("Node has no parent. moveToBottom function is ignored."),!1;let e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)}setZIndex(e){if(!this.parent)return s.Util.warn("Node has no parent. zIndex parameter is ignored."),this;(e<0||e>=this.parent.children.length)&&s.Util.warn("Unexpected value "+e+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");let t=this.index;return this.parent.children.splice(t,1),this.parent.children.splice(e,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(d,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){let e=this.opacity(),t=this.getParent();return t&&!t._isUnderCache&&(e*=t.getAbsoluteOpacity()),e}moveTo(e){return this.getParent()!==e&&(this._remove(),e.add(this)),this}toObject(){let e=this.getAttrs(),t,r,n,i,a={attrs:{},className:this.getClassName()};for(t in e)r=e[t],(!s.Util.isObject(r)||s.Util._isPlainObject(r)||s.Util._isArray(r))&&(n="function"==typeof this[t]&&this[t],delete e[t],i=n?n.call(this):null,e[t]=r,i!==r&&(a.attrs[t]=r));return s.Util._prepareToStringify(a)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(e,t,r){let n=[];t&&this._isMatch(e)&&n.push(this);let i=this.parent;for(;i&&i!==r;)i._isMatch(e)&&n.push(i),i=i.parent;return n}isAncestorOf(e){return!1}findAncestor(e,t,r){return this.findAncestors(e,t,r)[0]}_isMatch(e){if(!e)return!1;if("function"==typeof e)return e(this);let t=e.replace(/ /g,"").split(","),r=t.length,n,i;for(n=0;n<r;n++)if(i=t[n],s.Util.isValidSelector(i)||(s.Util.warn('Selector "'+i+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),s.Util.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),s.Util.warn("Konva is awesome, right?")),"#"===i.charAt(0)){if(this.id()===i.slice(1))return!0}else if("."===i.charAt(0)){if(this.hasName(i.slice(1)))return!0}else if(this.className===i||this.nodeType===i)return!0;return!1}getLayer(){let e=this.getParent();return e?e.getLayer():null}getStage(){return this._getCache(m,this._getStage)}_getStage(){let e=this.getParent();return e?e.getStage():null}fire(e,t={},r){return t.target=t.target||this,r?this._fireAndBubble(e,t):this._fire(e,t),this}getAbsoluteTransform(e){return e?this._getAbsoluteTransform(e):this._getCache(c,this._getAbsoluteTransform)}_getAbsoluteTransform(e){let t;if(e)return t=new s.Transform,this._eachAncestorReverse(function(e){let r=e.transformsEnabled();"all"===r?t.multiply(e.getTransform()):"position"===r&&t.translate(e.x()-e.offsetX(),e.y()-e.offsetY())},e),t;{t=this._cache.get(c)||new s.Transform,this.parent?this.parent.getAbsoluteTransform().copyInto(t):t.reset();let e=this.transformsEnabled();if("all"===e)t.multiply(this.getTransform());else if("position"===e){let e=this.attrs.x||0,r=this.attrs.y||0,n=this.attrs.offsetX||0,i=this.attrs.offsetY||0;t.translate(e-n,r-i)}return t.dirty=!1,t}}getAbsoluteScale(e){let t=this;for(;t;)t._isUnderCache&&(e=t),t=t.getParent();let r=this.getAbsoluteTransform(e).decompose();return{x:r.scaleX,y:r.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(v,this._getTransform)}_getTransform(){var e,t;let r=this._cache.get(v)||new s.Transform;r.reset();let n=this.x(),i=this.y(),a=o.Konva.getAngle(this.rotation()),l=null!=(e=this.attrs.scaleX)?e:1,d=null!=(t=this.attrs.scaleY)?t:1,h=this.attrs.skewX||0,c=this.attrs.skewY||0,u=this.attrs.offsetX||0,f=this.attrs.offsetY||0;return(0!==n||0!==i)&&r.translate(n,i),0!==a&&r.rotate(a),(0!==h||0!==c)&&r.skew(h,c),(1!==l||1!==d)&&r.scale(l,d),(0!==u||0!==f)&&r.translate(-1*u,-1*f),r.dirty=!1,r}clone(e){let t=s.Util.cloneObject(this.attrs),r,n,i,a,o;for(r in e)t[r]=e[r];let l=new this.constructor(t);for(r in this.eventListeners)for(a=0,i=(n=this.eventListeners[r]).length;a<i;a++)0>(o=n[a]).name.indexOf("konva")&&(l.eventListeners[r]||(l.eventListeners[r]=[]),l.eventListeners[r].push(o));return l}_toKonvaCanvas(e){e=e||{};let t=this.getClientRect(),r=this.getStage(),i=void 0!==e.x?e.x:Math.floor(t.x),a=void 0!==e.y?e.y:Math.floor(t.y),o=e.pixelRatio||1,s=new n.SceneCanvas({width:e.width||Math.ceil(t.width)||(r?r.width():0),height:e.height||Math.ceil(t.height)||(r?r.height():0),pixelRatio:o}),l=s.getContext(),d=new n.SceneCanvas({width:s.width/s.pixelRatio+Math.abs(i),height:s.height/s.pixelRatio+Math.abs(a),pixelRatio:s.pixelRatio});return!1===e.imageSmoothingEnabled&&(l._context.imageSmoothingEnabled=!1),l.save(),(i||a)&&l.translate(-1*i,-1*a),this.drawScene(s,void 0,d),l.restore(),s}toCanvas(e){return this._toKonvaCanvas(e)._canvas}toDataURL(e){let t=(e=e||{}).mimeType||null,r=e.quality||null,n=this._toKonvaCanvas(e).toDataURL(t,r);return e.callback&&e.callback(n),n}toImage(e){return new Promise((t,r)=>{try{let r=null==e?void 0:e.callback;r&&delete e.callback,s.Util._urlToImage(this.toDataURL(e),function(e){t(e),null==r||r(e)})}catch(e){r(e)}})}toBlob(e){return new Promise((t,r)=>{try{let r=null==e?void 0:e.callback;r&&delete e.callback,this.toCanvas(e).toBlob(e=>{t(e),null==r||r(e)},null==e?void 0:e.mimeType,null==e?void 0:e.quality)}catch(e){r(e)}})}setSize(e){return this.width(e.width),this.height(e.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():o.Konva.dragDistance}_off(e,t,r){let n=this.eventListeners[e],i,a,o;for(i=0;i<n.length;i++)if(a=n[i].name,o=n[i].handler,("konva"!==a||"konva"===t)&&(!t||a===t)&&(!r||r===o)){if(n.splice(i,1),0===n.length){delete this.eventListeners[e];break}i--}}_fireChangeEvent(e,t,r){this._fire(e+"Change",{oldVal:t,newVal:r})}addName(e){if(!this.hasName(e)){let t=this.name();this.name(t?t+" "+e:e)}return this}hasName(e){if(!e)return!1;let t=this.name();return!!t&&-1!==(t||"").split(/\s/g).indexOf(e)}removeName(e){let t=(this.name()||"").split(/\s/g),r=t.indexOf(e);return -1!==r&&(t.splice(r,1),this.name(t.join(" "))),this}setAttr(e,t){let r=this["set"+s.Util._capitalize(e)];return s.Util._isFunction(r)?r.call(this,t):this._setAttr(e,t),this}_requestDraw(){if(o.Konva.autoDrawEnabled){let e=this.getLayer()||this.getStage();null==e||e.batchDraw()}}_setAttr(e,t){let r=this.attrs[e];(r!==t||s.Util.isObject(t))&&(null==t?delete this.attrs[e]:this.attrs[e]=t,this._shouldFireChangeEvents&&this._fireChangeEvent(e,r,t),this._requestDraw())}_setComponentAttr(e,t,r){let n;void 0!==r&&((n=this.attrs[e])||(this.attrs[e]=this.getAttr(e)),this.attrs[e][t]=r,this._fireChangeEvent(e,n,r))}_fireAndBubble(e,t,r){t&&this.nodeType===g&&(t.target=this);let n=["mouseenter","mouseleave","pointerenter","pointerleave","touchenter","touchleave"];if(!(-1!==n.indexOf(e)&&(r&&(this===r||this.isAncestorOf&&this.isAncestorOf(r))||"Stage"===this.nodeType&&!r))){this._fire(e,t);let i=-1!==n.indexOf(e)&&r&&r.isAncestorOf&&r.isAncestorOf(this)&&!r.isAncestorOf(this.parent);(t&&!t.cancelBubble||!t)&&this.parent&&this.parent.isListening()&&!i&&(r&&r.parent?this._fireAndBubble.call(this.parent,e,t,r):this._fireAndBubble.call(this.parent,e,t))}}_getProtoListeners(e){var t,r,n;let i=null!=(t=this._cache.get(h))?t:{},a=null==i?void 0:i[e];if(void 0===a){a=[];let t=Object.getPrototypeOf(this);for(;t;){let i=null!=(n=null==(r=t.eventListeners)?void 0:r[e])?n:[];a.push(...i),t=Object.getPrototypeOf(t)}i[e]=a,this._cache.set(h,i)}return a}_fire(e,t){(t=t||{}).currentTarget=this,t.type=e;let r=this._getProtoListeners(e);if(r)for(let e=0;e<r.length;e++)r[e].handler.call(this,t);let n=this.eventListeners[e];if(n)for(let e=0;e<n.length;e++)n[e].handler.call(this,t)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(e){let t=e?e.pointerId:void 0,r=this.getStage(),n=this.getAbsolutePosition();if(!r)return;let a=r._getPointerById(t)||r._changedPointerPositions[0]||n;i.DD._dragElements.set(this._id,{node:this,startPointerPos:a,offset:{x:a.x-n.x,y:a.y-n.y},dragStatus:"ready",pointerId:t})}startDrag(e,t=!0){i.DD._dragElements.has(this._id)||this._createDragElement(e),i.DD._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:e&&e.evt},t)}_setDragPosition(e,t){let r=this.getStage()._getPointerById(t.pointerId);if(!r)return;let n={x:r.x-t.offset.x,y:r.y-t.offset.y},i=this.dragBoundFunc();if(void 0!==i){let t=i.call(this,n,e);t?n=t:s.Util.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}this._lastPos&&this._lastPos.x===n.x&&this._lastPos.y===n.y||(this.setAbsolutePosition(n),this._requestDraw()),this._lastPos=n}stopDrag(e){let t=i.DD._dragElements.get(this._id);t&&(t.dragStatus="stopped"),i.DD._endDragBefore(e),i.DD._endDragAfter(e)}setDraggable(e){this._setAttr("draggable",e),this._dragChange()}isDragging(){let e=i.DD._dragElements.get(this._id);return!!e&&"dragging"===e.dragStatus}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",function(e){if(!(void 0===e.evt.button||o.Konva.dragButtons.indexOf(e.evt.button)>=0)||this.isDragging())return;let t=!1;i.DD._dragElements.forEach(e=>{this.isAncestorOf(e.node)&&(t=!0)}),t||this._createDragElement(e)})}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{if(this._dragCleanup(),!this.getStage())return;let e=i.DD._dragElements.get(this._id),t=e&&"dragging"===e.dragStatus,r=e&&"ready"===e.dragStatus;t?this.stopDrag():r&&i.DD._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(e={x:0,y:0}){let t=this.getStage();if(!t)return!1;let r={x:-e.x,y:-e.y,width:t.width()+2*e.x,height:t.height()+2*e.y};return s.Util.haveIntersection(r,this.getClientRect())}static create(e,t){return s.Util._isString(e)&&(e=JSON.parse(e)),this._createNode(e,t)}static _createNode(e,t){let r=x.prototype.getClassName.call(e),n=e.children,i,a,l;if(t&&(e.attrs.container=t),o.Konva[r]||(s.Util.warn('Can not find a node with class name "'+r+'". Fallback to "Shape".'),r="Shape"),i=new o.Konva[r](e.attrs),n)for(l=0,a=n.length;l<a;l++)i.add(x._createNode(n[l]));return i}}t.Node=x,x.prototype.nodeType="Node",x.prototype._attrsAffectingSize=[],x.prototype.eventListeners={},x.prototype.on.call(x.prototype,"xChange.konva yChange.konva scaleXChange.konva scaleYChange.konva skewXChange.konva skewYChange.konva rotationChange.konva offsetXChange.konva offsetYChange.konva transformsEnabledChange.konva",function(){if(this._batchingTransformChange){this._needClearTransformCache=!0;return}this._clearCache(v),this._clearSelfAndDescendantCache(c)}),x.prototype.on.call(x.prototype,"visibleChange.konva",function(){this._clearSelfAndDescendantCache(y)}),x.prototype.on.call(x.prototype,"listeningChange.konva",function(){this._clearSelfAndDescendantCache(p)}),x.prototype.on.call(x.prototype,"opacityChange.konva",function(){this._clearSelfAndDescendantCache(d)});let w=a.Factory.addGetterSetter;w(x,"zIndex"),w(x,"absolutePosition"),w(x,"position"),w(x,"x",0,(0,l.getNumberValidator)()),w(x,"y",0,(0,l.getNumberValidator)()),w(x,"globalCompositeOperation","source-over",(0,l.getStringValidator)()),w(x,"opacity",1,(0,l.getNumberValidator)()),w(x,"name","",(0,l.getStringValidator)()),w(x,"id","",(0,l.getStringValidator)()),w(x,"rotation",0,(0,l.getNumberValidator)()),a.Factory.addComponentsGetterSetter(x,"scale",["x","y"]),w(x,"scaleX",1,(0,l.getNumberValidator)()),w(x,"scaleY",1,(0,l.getNumberValidator)()),a.Factory.addComponentsGetterSetter(x,"skew",["x","y"]),w(x,"skewX",0,(0,l.getNumberValidator)()),w(x,"skewY",0,(0,l.getNumberValidator)()),a.Factory.addComponentsGetterSetter(x,"offset",["x","y"]),w(x,"offsetX",0,(0,l.getNumberValidator)()),w(x,"offsetY",0,(0,l.getNumberValidator)()),w(x,"dragDistance",void 0,(0,l.getNumberValidator)()),w(x,"width",0,(0,l.getNumberValidator)()),w(x,"height",0,(0,l.getNumberValidator)()),w(x,"listening",!0,(0,l.getBooleanValidator)()),w(x,"preventDefault",!0,(0,l.getBooleanValidator)()),w(x,"filters",void 0,function(e){return this._filterUpToDate=!1,e}),w(x,"visible",!0,(0,l.getBooleanValidator)()),w(x,"transformsEnabled","all",(0,l.getStringValidator)()),w(x,"size"),w(x,"dragBoundFunc"),w(x,"draggable",!1,(0,l.getBooleanValidator)()),a.Factory.backCompat(x,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"})},968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(2115),i=r(3655),a=r(5155),o=n.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1144:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextPath=void 0;let n=r(6509),i=r(8949),a=r(3306),o=r(3927),s=r(3215),l=r(6394),d=r(6878),h="normal";function c(e){e.fillText(this.partialText,0,0)}function u(e){e.strokeText(this.partialText,0,0)}class f extends a.Shape{constructor(e){super(e),this.dummyCanvas=n.Util.createCanvasElement(),this.dataArray=[],this._readDataAttribute(),this.on("dataChange.konva",function(){this._readDataAttribute(),this._setTextData()}),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_getTextPathLength(){return o.Path.getPathLength(this.dataArray)}_getPointAtLength(e){return!this.attrs.data||e-1>this.pathLength?null:o.Path.getPointAtLengthOfDataArray(e,this.dataArray)}_readDataAttribute(){this.dataArray=o.Path.parsePathData(this.attrs.data),this.pathLength=this._getTextPathLength()}_sceneFunc(e){e.setAttr("font",this._getContextFont()),e.setAttr("textBaseline",this.textBaseline()),e.setAttr("textAlign","left"),e.save();let t=this.textDecoration(),r=this.fill(),n=this.fontSize(),i=this.glyphInfo;"underline"===t&&e.beginPath();for(let r=0;r<i.length;r++){e.save();let a=i[r].p0;e.translate(a.x,a.y),e.rotate(i[r].rotation),this.partialText=i[r].text,e.fillStrokeShape(this),"underline"===t&&(0===r&&e.moveTo(0,n/2+1),e.lineTo(n,n/2+1)),e.restore()}"underline"===t&&(e.strokeStyle=r,e.lineWidth=n/20,e.stroke()),e.restore()}_hitFunc(e){e.beginPath();let t=this.glyphInfo;if(t.length>=1){let r=t[0].p0;e.moveTo(r.x,r.y)}for(let r=0;r<t.length;r++){let n=t[r].p1;e.lineTo(n.x,n.y)}e.setAttr("lineWidth",this.fontSize()),e.setAttr("strokeStyle",this.colorKey),e.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return n.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(e){return s.Text.prototype.setText.call(this,e)}_getContextFont(){return s.Text.prototype._getContextFont.call(this)}_getTextSize(e){let t=this.dummyCanvas.getContext("2d");t.save(),t.font=this._getContextFont();let r=t.measureText(e);return t.restore(),{width:r.width,height:parseInt(`${this.fontSize()}`,10)}}_setTextData(){let{width:e,height:t}=this._getTextSize(this.attrs.text);if(this.textWidth=e,this.textHeight=t,this.glyphInfo=[],!this.attrs.data)return null;let r=this.letterSpacing(),n=this.align(),i=this.kerningFunc(),a=Math.max(this.textWidth+((this.attrs.text||"").length-1)*r,0),l=0;"center"===n&&(l=Math.max(0,this.pathLength/2-a/2)),"right"===n&&(l=Math.max(0,this.pathLength-a));let d=(0,s.stringToArray)(this.text()),h=l;for(let e=0;e<d.length;e++){let t=this._getPointAtLength(h);if(!t)return;let s=this._getTextSize(d[e]).width+r;if(" "===d[e]&&"justify"===n){let e=this.text().split(" ").length-1;s+=(this.pathLength-a)/e}let l=this._getPointAtLength(h+s);if(!l)return;let c=o.Path.getLineLength(t.x,t.y,l.x,l.y),u=0;if(i)try{u=i(d[e-1],d[e])*this.fontSize()}catch(e){u=0}t.x+=u,l.x+=u,this.textWidth+=u;let f=o.Path.getPointOnLine(u+c/2,t.x,t.y,l.x,l.y),p=Math.atan2(l.y-t.y,l.x-t.x);this.glyphInfo.push({transposeX:f.x,transposeY:f.y,text:d[e],rotation:p,p0:t,p1:l}),h+=s}}getSelfRect(){let e,t;if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};let r=[];this.glyphInfo.forEach(function(e){r.push(e.p0.x),r.push(e.p0.y),r.push(e.p1.x),r.push(e.p1.y)});let n=r[0]||0,i=r[0]||0,a=r[1]||0,o=r[1]||0;for(let s=0;s<r.length/2;s++)e=r[2*s],t=r[2*s+1],n=Math.min(n,e),i=Math.max(i,e),a=Math.min(a,t),o=Math.max(o,t);let s=this.fontSize();return{x:n-s/2,y:a-s/2,width:i-n+s,height:o-a+s}}destroy(){return n.Util.releaseCanvas(this.dummyCanvas),super.destroy()}}t.TextPath=f,f.prototype._fillFunc=c,f.prototype._strokeFunc=u,f.prototype._fillFuncHit=c,f.prototype._strokeFuncHit=u,f.prototype.className="TextPath",f.prototype._attrsAffectingSize=["text","fontSize","data"],(0,d._registerNode)(f),i.Factory.addGetterSetter(f,"data"),i.Factory.addGetterSetter(f,"fontFamily","Arial"),i.Factory.addGetterSetter(f,"fontSize",12,(0,l.getNumberValidator)()),i.Factory.addGetterSetter(f,"fontStyle",h),i.Factory.addGetterSetter(f,"align","left"),i.Factory.addGetterSetter(f,"letterSpacing",0,(0,l.getNumberValidator)()),i.Factory.addGetterSetter(f,"textBaseline","middle"),i.Factory.addGetterSetter(f,"fontVariant",h),i.Factory.addGetterSetter(f,"text",""),i.Factory.addGetterSetter(f,"textDecoration",""),i.Factory.addGetterSetter(f,"kerningFunc",void 0)},1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1231:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RGBA=void 0;let n=r(8949),i=r(913),a=r(6394);t.RGBA=function(e){let t=e.data,r=t.length,n=this.red(),i=this.green(),a=this.blue(),o=this.alpha();for(let e=0;e<r;e+=4){let r=1-o;t[e]=n*o+t[e]*r,t[e+1]=i*o+t[e+1]*r,t[e+2]=a*o+t[e+2]*r}},n.Factory.addGetterSetter(i.Node,"red",0,function(e){return(this._filterUpToDate=!1,e>255)?255:e<0?0:Math.round(e)}),n.Factory.addGetterSetter(i.Node,"green",0,function(e){return(this._filterUpToDate=!1,e>255)?255:e<0?0:Math.round(e)}),n.Factory.addGetterSetter(i.Node,"blue",0,a.RGBComponent,n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"alpha",1,function(e){return(this._filterUpToDate=!1,e>1)?1:e<0?0:e})},1248:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Container=void 0;let n=r(8949),i=r(913),a=r(6394);class o extends i.Node{constructor(){super(...arguments),this.children=[]}getChildren(e){let t=this.children||[];return e?t.filter(e):t}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach(e=>{e.parent=null,e.index=0,e.remove()}),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach(e=>{e.parent=null,e.index=0,e.destroy()}),this.children=[],this._requestDraw(),this}add(...e){if(0===e.length)return this;if(e.length>1){for(let t=0;t<e.length;t++)this.add(e[t]);return this}let t=e[0];return t.getParent()?t.moveTo(this):(this._validateAdd(t),t.index=this.getChildren().length,t.parent=this,t._clearCaches(),this.getChildren().push(t),this._fire("add",{child:t}),this._requestDraw()),this}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(e){return this._generalFind(e,!1)}findOne(e){let t=this._generalFind(e,!0);return t.length>0?t[0]:void 0}_generalFind(e,t){let r=[];return this._descendants(n=>{let i=n._isMatch(e);return i&&r.push(n),!!i&&!!t}),r}_descendants(e){for(let t of this.getChildren())if(e(t)||t.hasChildren()&&t._descendants(e))return!0;return!1}toObject(){let e=i.Node.prototype.toObject.call(this);return e.children=[],this.getChildren().forEach(t=>{e.children.push(t.toObject())}),e}isAncestorOf(e){let t=e.getParent();for(;t;){if(t._id===this._id)return!0;t=t.getParent()}return!1}clone(e){let t=i.Node.prototype.clone.call(this,e);return this.getChildren().forEach(function(e){t.add(e.clone())}),t}getAllIntersections(e){let t=[];return this.find("Shape").forEach(r=>{r.isVisible()&&r.intersects(e)&&t.push(r)}),t}_clearSelfAndDescendantCache(e){var t;super._clearSelfAndDescendantCache(e),this.isCached()||null==(t=this.children)||t.forEach(function(t){t._clearSelfAndDescendantCache(e)})}_setChildrenIndices(){var e;null==(e=this.children)||e.forEach(function(e,t){e.index=t}),this._requestDraw()}drawScene(e,t,r){let n=this.getLayer(),i=e||n&&n.getCanvas(),a=i&&i.getContext(),o=this._getCanvasCache(),s=o&&o.scene,l=i&&i.isCache;if(!this.isVisible()&&!l)return this;if(s){a.save();let e=this.getAbsoluteTransform(t).getMatrix();a.transform(e[0],e[1],e[2],e[3],e[4],e[5]),this._drawCachedSceneCanvas(a),a.restore()}else this._drawChildren("drawScene",i,t,r);return this}drawHit(e,t){if(!this.shouldDrawHit(t))return this;let r=this.getLayer(),n=e||r&&r.hitCanvas,i=n&&n.getContext(),a=this._getCanvasCache();if(a&&a.hit){i.save();let e=this.getAbsoluteTransform(t).getMatrix();i.transform(e[0],e[1],e[2],e[3],e[4],e[5]),this._drawCachedHitCanvas(i),i.restore()}else this._drawChildren("drawHit",n,t);return this}_drawChildren(e,t,r,n){var i;let a=t&&t.getContext(),o=this.clipWidth(),s=this.clipHeight(),l=this.clipFunc(),d="number"==typeof o&&"number"==typeof s||l,h=r===this;if(d){let e;a.save();let t=this.getAbsoluteTransform(r),n=t.getMatrix();if(a.transform(n[0],n[1],n[2],n[3],n[4],n[5]),a.beginPath(),l)e=l.call(this,a,this);else{let e=this.clipX(),t=this.clipY();a.rect(e||0,t||0,o,s)}a.clip.apply(a,e),n=t.copy().invert().getMatrix(),a.transform(n[0],n[1],n[2],n[3],n[4],n[5])}let c=!h&&"source-over"!==this.globalCompositeOperation()&&"drawScene"===e;c&&(a.save(),a._applyGlobalCompositeOperation(this)),null==(i=this.children)||i.forEach(function(i){i[e](t,r,n)}),c&&a.restore(),d&&a.restore()}getClientRect(e={}){var t;let r,n,i,a,o=e.skipTransform,s=e.relativeTo,l={x:1/0,y:1/0,width:0,height:0},d=this;null==(t=this.children)||t.forEach(function(t){if(!t.visible())return;let o=t.getClientRect({relativeTo:d,skipShadow:e.skipShadow,skipStroke:e.skipStroke});(0!==o.width||0!==o.height)&&(void 0===r?(r=o.x,n=o.y,i=o.x+o.width,a=o.y+o.height):(r=Math.min(r,o.x),n=Math.min(n,o.y),i=Math.max(i,o.x+o.width),a=Math.max(a,o.y+o.height)))});let h=this.find("Shape"),c=!1;for(let e=0;e<h.length;e++)if(h[e]._isVisible(this)){c=!0;break}return(l=c&&void 0!==r?{x:r,y:n,width:i-r,height:a-n}:{x:0,y:0,width:0,height:0},o)?l:this._transformedRect(l,s)}}t.Container=o,n.Factory.addComponentsGetterSetter(o,"clip",["x","y","width","height"]),n.Factory.addGetterSetter(o,"clipX",void 0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(o,"clipY",void 0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(o,"clipWidth",void 0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(o,"clipHeight",void 0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(o,"clipFunc")},1249:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Sepia=void 0,t.Sepia=function(e){let t=e.data,r=t.length;for(let e=0;e<r;e+=4){let r=t[e+0],n=t[e+1],i=t[e+2];t[e+0]=Math.min(255,.393*r+.769*n+.189*i),t[e+1]=Math.min(255,.349*r+.686*n+.168*i),t[e+2]=Math.min(255,.272*r+.534*n+.131*i)}}},1275:(e,t,r)=>{r.d(t,{X:()=>a});var n=r(2115),i=r(2712);function a(e){let[t,r]=n.useState(void 0);return(0,i.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},1285:(e,t,r)=>{r.d(t,{B:()=>l});var n,i=r(2115),a=r(2712),o=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=i.useState(o());return(0,a.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},1366:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1414:(e,t,r)=>{e.exports=r(2436)},1788:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1933:(e,t,r)=>{e.exports=r(6500)},1976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("heart",[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]])},2085:(e,t,r)=>{r.d(t,{F:()=>o});var n=r(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:s}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let a=i(t)||i(n);return o[e][a]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...d}[t]):({...s,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2103:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("mouse-pointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]])},2104:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Grayscale=void 0,t.Grayscale=function(e){let t=e.data,r=t.length;for(let e=0;e<r;e+=4){let r=.34*t[e]+.5*t[e+1]+.16*t[e+2];t[e]=r,t[e+1]=r,t[e+2]=r}}},2293:(e,t,r)=>{r.d(t,{Oh:()=>a});var n=r(2115),i=0;function a(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:o()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:o()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function o(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2407:(e,t,r)=>{e.exports=r(6892)},2436:(e,t,r)=>{var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var h="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,h=n[1];return s(function(){i.value=r,i.getSnapshot=t,d(i)&&h({inst:i})},[e,r,t]),o(function(){return d(i)&&h({inst:i}),e(function(){d(i)&&h({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:h},2465:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;let n=r(6878),i=r(6509),a=r(913),o=r(1248),s=r(773),l=r(7498),d=r(7728),h=r(836),c=r(165),u=r(3306),f=r(6345),p=r(3424),g=r(9012),m=r(4421);t.Konva=i.Util._assign(n.Konva,{Util:i.Util,Transform:i.Transform,Node:a.Node,Container:o.Container,Stage:s.Stage,stages:s.stages,Layer:l.Layer,FastLayer:d.FastLayer,Group:h.Group,DD:c.DD,Shape:u.Shape,shapes:u.shapes,Animation:f.Animation,Tween:p.Tween,Easings:p.Easings,Context:g.Context,Canvas:m.Canvas}),t.default=t.Konva},2525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},2568:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}},2657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(9991),i=r(7102);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},2670:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;var n=r(2465);Object.defineProperty(t,"Konva",{enumerable:!0,get:function(){return n.Konva}}),e.exports=r(2465).Konva},2712:(e,t,r)=>{r.d(t,{N:()=>i});var n=r(2115),i=globalThis?.document?n.useLayoutEffect:()=>{}},2757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let n=r(6966)._(r(8859)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",o=e.pathname||"",s=e.hash||"",l=e.query||"",d=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?d=t+e.host:r&&(d=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(d+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==d?(d="//"+(d||""),o&&"/"!==o[0]&&(o="/"+o)):d||(d=""),s&&"#"!==s[0]&&(s="#"+s),h&&"?"!==h[0]&&(h="?"+h),""+a+d+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},2918:(e,t,r)=>{r.d(t,{UC:()=>eO,YJ:()=>eI,In:()=>eN,q7:()=>ej,VF:()=>eB,p4:()=>eU,JU:()=>eG,ZL:()=>eF,bL:()=>eE,wn:()=>eH,PP:()=>ez,wv:()=>eV,l9:()=>eR,WT:()=>eD,LM:()=>eL});var n=r(2115),i=r(7650),a=r(9367),o=r(5185),s=r(7328),l=r(6101),d=r(6081),h=r(4315),c=r(9178),u=r(2293),f=r(7900),p=r(1285),g=r(5152),m=r(4378),v=r(3655),y=r(9708),b=r(9033),x=r(5845),w=r(2712),_=r(5503),S=r(5155),C=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,S.jsx)(v.sG.span,{...e,ref:t,style:{...C,...e.style}})).displayName="VisuallyHidden";var k=r(8168),P=r(3795),A=[" ","Enter","ArrowUp","ArrowDown"],M=[" ","Enter"],T="Select",[E,R,D]=(0,s.N)(T),[N,F]=(0,d.A)(T,[D,g.Bk]),O=(0,g.Bk)(),[L,I]=N(T),[G,j]=N(T),U=e=>{let{__scopeSelect:t,children:r,open:i,defaultOpen:a,onOpenChange:o,value:s,defaultValue:l,onValueChange:d,dir:c,name:u,autoComplete:f,disabled:m,required:v,form:y}=e,b=O(t),[w,_]=n.useState(null),[C,k]=n.useState(null),[P,A]=n.useState(!1),M=(0,h.jH)(c),[R,D]=(0,x.i)({prop:i,defaultProp:null!=a&&a,onChange:o,caller:T}),[N,F]=(0,x.i)({prop:s,defaultProp:l,onChange:d,caller:T}),I=n.useRef(null),j=!w||y||!!w.closest("form"),[U,B]=n.useState(new Set),z=Array.from(U).map(e=>e.props.value).join(";");return(0,S.jsx)(g.bL,{...b,children:(0,S.jsxs)(L,{required:v,scope:t,trigger:w,onTriggerChange:_,valueNode:C,onValueNodeChange:k,valueNodeHasChildren:P,onValueNodeHasChildrenChange:A,contentId:(0,p.B)(),value:N,onValueChange:F,open:R,onOpenChange:D,dir:M,triggerPointerDownPosRef:I,disabled:m,children:[(0,S.jsx)(E.Provider,{scope:t,children:(0,S.jsx)(G,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{B(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{B(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),j?(0,S.jsxs)(eP,{"aria-hidden":!0,required:v,tabIndex:-1,name:u,autoComplete:f,value:N,onChange:e=>F(e.target.value),disabled:m,form:y,children:[void 0===N?(0,S.jsx)("option",{value:""}):null,Array.from(U)]},z):null]})})};U.displayName=T;var B="SelectTrigger",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:i=!1,...a}=e,s=O(r),d=I(B,r),h=d.disabled||i,c=(0,l.s)(t,d.onTriggerChange),u=R(r),f=n.useRef("touch"),[p,m,y]=eM(e=>{let t=u().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eT(t,e,r);void 0!==n&&d.onValueChange(n.value)}),b=e=>{h||(d.onOpenChange(!0),y()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(g.Mz,{asChild:!0,...s,children:(0,S.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":eA(d.value)?"":void 0,...a,ref:c,onClick:(0,o.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&b(e)}),onPointerDown:(0,o.m)(a.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,o.m)(a.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&A.includes(e.key)&&(b(),e.preventDefault())})})})});z.displayName=B;var H="SelectValue",V=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,children:a,placeholder:o="",...s}=e,d=I(H,r),{onValueNodeHasChildrenChange:h}=d,c=void 0!==a,u=(0,l.s)(t,d.onValueNodeChange);return(0,w.N)(()=>{h(c)},[h,c]),(0,S.jsx)(v.sG.span,{...s,ref:u,style:{pointerEvents:"none"},children:eA(d.value)?(0,S.jsx)(S.Fragment,{children:o}):a})});V.displayName=H;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...i}=e;return(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...i,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var K=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});K.displayName="SelectPortal";var Y="SelectContent",q=n.forwardRef((e,t)=>{let r=I(Y,e.__scopeSelect),[a,o]=n.useState();return((0,w.N)(()=>{o(new DocumentFragment)},[]),r.open)?(0,S.jsx)(Q,{...e,ref:t}):a?i.createPortal((0,S.jsx)(X,{scope:e.__scopeSelect,children:(0,S.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),a):null});q.displayName=Y;var[X,$]=N(Y),Z=(0,y.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:i="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:s,onPointerDownOutside:d,side:h,sideOffset:p,align:g,alignOffset:m,arrowPadding:v,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:w,avoidCollisions:_,...C}=e,A=I(Y,r),[M,T]=n.useState(null),[E,D]=n.useState(null),N=(0,l.s)(t,e=>T(e)),[F,O]=n.useState(null),[L,G]=n.useState(null),j=R(r),[U,B]=n.useState(!1),z=n.useRef(!1);n.useEffect(()=>{if(M)return(0,k.Eq)(M)},[M]),(0,u.Oh)();let H=n.useCallback(e=>{let[t,...r]=j().map(e=>e.ref.current),[n]=r.slice(-1),i=document.activeElement;for(let r of e)if(r===i||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),null==r||r.focus(),document.activeElement!==i))return},[j,E]),V=n.useCallback(()=>H([F,M]),[H,F,M]);n.useEffect(()=>{U&&V()},[U,V]);let{onOpenChange:W,triggerPointerDownPosRef:K}=A;n.useEffect(()=>{if(M){let e={x:0,y:0},t=t=>{var r,n,i,a;e={x:Math.abs(Math.round(t.pageX)-(null!=(i=null==(r=K.current)?void 0:r.x)?i:0)),y:Math.abs(Math.round(t.pageY)-(null!=(a=null==(n=K.current)?void 0:n.y)?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():M.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[M,W,K]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[q,$]=eM(e=>{let t=j().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==A.value&&A.value===t||n)&&(O(e),n&&(z.current=!0))},[A.value]),et=n.useCallback(()=>null==M?void 0:M.focus(),[M]),er=n.useCallback((e,t,r)=>{let n=!z.current&&!r;(void 0!==A.value&&A.value===t||n)&&G(e)},[A.value]),en="popper"===i?ee:J,ei=en===ee?{side:h,sideOffset:p,align:g,alignOffset:m,arrowPadding:v,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:w,avoidCollisions:_}:{};return(0,S.jsx)(X,{scope:r,content:M,viewport:E,onViewportChange:D,itemRefCallback:Q,selectedItem:F,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:V,selectedItemText:L,position:i,isPositioned:U,searchRef:q,children:(0,S.jsx)(P.A,{as:Z,allowPinchZoom:!0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.m)(a,e=>{var t;null==(t=A.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,S.jsx)(en,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...C,...ei,onPlaced:()=>B(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=j().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:i,...o}=e,s=I(Y,r),d=$(Y,r),[h,c]=n.useState(null),[u,f]=n.useState(null),p=(0,l.s)(t,e=>f(e)),g=R(r),m=n.useRef(!1),y=n.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:_,focusSelectedItem:C}=d,k=n.useCallback(()=>{if(s.trigger&&s.valueNode&&h&&u&&b&&x&&_){let e=s.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),r=s.valueNode.getBoundingClientRect(),n=_.getBoundingClientRect();if("rtl"!==s.dir){let i=n.left-t.left,o=r.left-i,s=e.left-o,l=e.width+s,d=Math.max(l,t.width),c=window.innerWidth-10,u=(0,a.q)(o,[10,Math.max(10,c-d)]);h.style.minWidth=l+"px",h.style.left=u+"px"}else{let i=t.right-n.right,o=window.innerWidth-r.right-i,s=window.innerWidth-e.right-o,l=e.width+s,d=Math.max(l,t.width),c=window.innerWidth-10,u=(0,a.q)(o,[10,Math.max(10,c-d)]);h.style.minWidth=l+"px",h.style.right=u+"px"}let o=g(),l=window.innerHeight-20,d=b.scrollHeight,c=window.getComputedStyle(u),f=parseInt(c.borderTopWidth,10),p=parseInt(c.paddingTop,10),v=parseInt(c.borderBottomWidth,10),y=f+p+d+parseInt(c.paddingBottom,10)+v,w=Math.min(5*x.offsetHeight,y),S=window.getComputedStyle(b),C=parseInt(S.paddingTop,10),k=parseInt(S.paddingBottom,10),P=e.top+e.height/2-10,A=x.offsetHeight/2,M=f+p+(x.offsetTop+A);if(M<=P){let e=o.length>0&&x===o[o.length-1].ref.current;h.style.bottom="0px";let t=Math.max(l-P,A+(e?k:0)+(u.clientHeight-b.offsetTop-b.offsetHeight)+v);h.style.height=M+t+"px"}else{let e=o.length>0&&x===o[0].ref.current;h.style.top="0px";let t=Math.max(P,f+b.offsetTop+(e?C:0)+A);h.style.height=t+(y-M)+"px",b.scrollTop=M-P+b.offsetTop}h.style.margin="".concat(10,"px 0"),h.style.minHeight=w+"px",h.style.maxHeight=l+"px",null==i||i(),requestAnimationFrame(()=>m.current=!0)}},[g,s.trigger,s.valueNode,h,u,b,x,_,s.dir,i]);(0,w.N)(()=>k(),[k]);let[P,A]=n.useState();(0,w.N)(()=>{u&&A(window.getComputedStyle(u).zIndex)},[u]);let M=n.useCallback(e=>{e&&!0===y.current&&(k(),null==C||C(),y.current=!1)},[k,C]);return(0,S.jsx)(et,{scope:r,contentWrapper:h,shouldExpandOnScrollRef:m,onScrollButtonChange:M,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:(0,S.jsx)(v.sG.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});J.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:i=10,...a}=e,o=O(r);return(0,S.jsx)(g.UC,{...o,...a,ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(Y,{}),en="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:i,...a}=e,s=$(en,r),d=er(en,r),h=(0,l.s)(t,s.onViewportChange),c=n.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,S.jsx)(E.Slot,{scope:r,children:(0,S.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:h,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,o.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,i=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(i<n){let a=i+e,o=Math.min(n,a),s=a-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=s>0?s:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ei.displayName=en;var ea="SelectGroup",[eo,es]=N(ea),el=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=(0,p.B)();return(0,S.jsx)(eo,{scope:r,id:i,children:(0,S.jsx)(v.sG.div,{role:"group","aria-labelledby":i,...n,ref:t})})});el.displayName=ea;var ed="SelectLabel",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=es(ed,r);return(0,S.jsx)(v.sG.div,{id:i.id,...n,ref:t})});eh.displayName=ed;var ec="SelectItem",[eu,ef]=N(ec),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:i,disabled:a=!1,textValue:s,...d}=e,h=I(ec,r),c=$(ec,r),u=h.value===i,[f,g]=n.useState(null!=s?s:""),[m,y]=n.useState(!1),b=(0,l.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,i,a)}),x=(0,p.B)(),w=n.useRef("touch"),_=()=>{a||(h.onValueChange(i),h.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(eu,{scope:r,value:i,disabled:a,textId:x,isSelected:u,onItemTextChange:n.useCallback(e=>{g(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,S.jsx)(E.ItemSlot,{scope:r,value:i,disabled:a,textValue:f,children:(0,S.jsx)(v.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":u&&m,"data-state":u?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...d,ref:b,onFocus:(0,o.m)(d.onFocus,()=>y(!0)),onBlur:(0,o.m)(d.onBlur,()=>y(!1)),onClick:(0,o.m)(d.onClick,()=>{"mouse"!==w.current&&_()}),onPointerUp:(0,o.m)(d.onPointerUp,()=>{"mouse"===w.current&&_()}),onPointerDown:(0,o.m)(d.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,o.m)(d.onPointerMove,e=>{if(w.current=e.pointerType,a){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,o.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(M.includes(e.key)&&_()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ec;var eg="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:o,...s}=e,d=I(eg,r),h=$(eg,r),c=ef(eg,r),u=j(eg,r),[f,p]=n.useState(null),g=(0,l.s)(t,e=>p(e),c.onItemTextChange,e=>{var t;return null==(t=h.itemTextRefCallback)?void 0:t.call(h,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,y=n.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=u;return(0,w.N)(()=>(b(y),()=>x(y)),[b,x,y]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(v.sG.span,{id:c.textId,...s,ref:g}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?i.createPortal(s.children,d.valueNode):null]})});em.displayName=eg;var ev="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ev,r).isSelected?(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=ev;var eb="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=$(eb,e.__scopeSelect),i=er(eb,e.__scopeSelect),[a,o]=n.useState(!1),s=(0,l.s)(t,i.onScrollButtonChange);return(0,w.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,S.jsx)(eS,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=eb;var ew="SelectScrollDownButton",e_=n.forwardRef((e,t)=>{let r=$(ew,e.__scopeSelect),i=er(ew,e.__scopeSelect),[a,o]=n.useState(!1),s=(0,l.s)(t,i.onScrollButtonChange);return(0,w.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,S.jsx)(eS,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e_.displayName=ew;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:i,...a}=e,s=$("SelectScrollButton",r),l=n.useRef(null),d=R(r),h=n.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return n.useEffect(()=>()=>h(),[h]),(0,w.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,o.m)(a.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(i,50))}),onPointerMove:(0,o.m)(a.onPointerMove,()=>{var e;null==(e=s.onItemLeave)||e.call(s),null===l.current&&(l.current=window.setInterval(i,50))}),onPointerLeave:(0,o.m)(a.onPointerLeave,()=>{h()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=O(r),a=I(ek,r),o=$(ek,r);return a.open&&"popper"===o.position?(0,S.jsx)(g.i3,{...i,...n,ref:t}):null}).displayName=ek;var eP=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:i,...a}=e,o=n.useRef(null),s=(0,l.s)(t,o),d=(0,_.Z)(i);return n.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==i&&t){let r=new Event("change",{bubbles:!0});t.call(e,i),e.dispatchEvent(r)}},[d,i]),(0,S.jsx)(v.sG.select,{...a,style:{...C,...a.style},ref:s,defaultValue:i})});function eA(e){return""===e||void 0===e}function eM(e){let t=(0,b.c)(e),r=n.useRef(""),i=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(i.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(i.current),[]),[r,a,o]}function eT(e,t,r){var n,i;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,s=(n=e,i=Math.max(o,0),n.map((e,t)=>n[(i+t)%n.length]));1===a.length&&(s=s.filter(e=>e!==r));let l=s.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return l!==r?l:void 0}eP.displayName="SelectBubbleInput";var eE=U,eR=z,eD=V,eN=W,eF=K,eO=q,eL=ei,eI=el,eG=eh,ej=ep,eU=em,eB=ey,ez=ex,eH=e_,eV=eC},2967:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;let n=r(2465),i=r(9340),a=r(5673),o=r(5344),s=r(5010),l=r(4185),d=r(9482),h=r(8650),c=r(3927),u=r(3186),f=r(6466),p=r(6090),g=r(3053),m=r(3624),v=r(3215),y=r(1144),b=r(7371),x=r(9408),w=r(9732),_=r(8770),S=r(6955),C=r(9902),k=r(801),P=r(2104),A=r(8116),M=r(6522),T=r(8197),E=r(48),R=r(9705),D=r(3529),N=r(29),F=r(9490),O=r(9480),L=r(1231),I=r(1249),G=r(748),j=r(9044);t.Konva=n.Konva.Util._assign(n.Konva,{Arc:i.Arc,Arrow:a.Arrow,Circle:o.Circle,Ellipse:s.Ellipse,Image:l.Image,Label:d.Label,Tag:d.Tag,Line:h.Line,Path:c.Path,Rect:u.Rect,RegularPolygon:f.RegularPolygon,Ring:p.Ring,Sprite:g.Sprite,Star:m.Star,Text:v.Text,TextPath:y.TextPath,Transformer:b.Transformer,Wedge:x.Wedge,Filters:{Blur:w.Blur,Brighten:_.Brighten,Contrast:S.Contrast,Emboss:C.Emboss,Enhance:k.Enhance,Grayscale:P.Grayscale,HSL:A.HSL,HSV:M.HSV,Invert:T.Invert,Kaleidoscope:E.Kaleidoscope,Mask:R.Mask,Noise:D.Noise,Pixelate:N.Pixelate,Posterize:F.Posterize,RGB:O.RGB,RGBA:L.RGBA,Sepia:I.Sepia,Solarize:G.Solarize,Threshold:j.Threshold}})},3052:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3053:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Sprite=void 0;let n=r(8949),i=r(3306),a=r(6345),o=r(6394),s=r(6878);class l extends i.Shape{constructor(e){super(e),this._updated=!0,this.anim=new a.Animation(()=>{let e=this._updated;return this._updated=!1,e}),this.on("animationChange.konva",function(){this.frameIndex(0)}),this.on("frameIndexChange.konva",function(){this._updated=!0}),this.on("frameRateChange.konva",function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())})}_sceneFunc(e){let t=this.animation(),r=this.frameIndex(),n=4*r,i=this.animations()[t],a=this.frameOffsets(),o=i[n+0],s=i[n+1],l=i[n+2],d=i[n+3],h=this.image();if((this.hasFill()||this.hasStroke())&&(e.beginPath(),e.rect(0,0,l,d),e.closePath(),e.fillStrokeShape(this)),h)if(a){let n=a[t],i=2*r;e.drawImage(h,o,s,l,d,n[i+0],n[i+1],l,d)}else e.drawImage(h,o,s,l,d,0,0,l,d)}_hitFunc(e){let t=this.animation(),r=this.frameIndex(),n=4*r,i=this.animations()[t],a=this.frameOffsets(),o=i[n+2],s=i[n+3];if(e.beginPath(),a){let n=a[t],i=2*r;e.rect(n[i+0],n[i+1],o,s)}else e.rect(0,0,o,s);e.closePath(),e.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){let e=this;this.interval=setInterval(function(){e._updateIndex()},1e3/this.frameRate())}start(){if(this.isRunning())return;let e=this.getLayer();this.anim.setLayers(e),this._setInterval(),this.anim.start()}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){let e=this.frameIndex(),t=this.animation();e<this.animations()[t].length/4-1?this.frameIndex(e+1):this.frameIndex(0)}}t.Sprite=l,l.prototype.className="Sprite",(0,s._registerNode)(l),n.Factory.addGetterSetter(l,"animation"),n.Factory.addGetterSetter(l,"animations"),n.Factory.addGetterSetter(l,"frameOffsets"),n.Factory.addGetterSetter(l,"image"),n.Factory.addGetterSetter(l,"frameIndex",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"frameRate",17,(0,o.getNumberValidator)()),n.Factory.backCompat(l,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"})},3127:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},3186:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Rect=void 0;let n=r(8949),i=r(3306),a=r(6878),o=r(6509),s=r(6394);class l extends i.Shape{_sceneFunc(e){let t=this.cornerRadius(),r=this.width(),n=this.height();e.beginPath(),t?o.Util.drawRoundedRectPath(e,r,n,t):e.rect(0,0,r,n),e.closePath(),e.fillStrokeShape(this)}}t.Rect=l,l.prototype.className="Rect",(0,a._registerNode)(l),n.Factory.addGetterSetter(l,"cornerRadius",0,(0,s.getNumberOrArrayOfNumbersValidator)(4))},3215:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),t.Text=void 0,t.stringToArray=h;let i=r(6509),a=r(8949),o=r(3306),s=r(6878),l=r(6394),d=r(6878);function h(e){return[...e].reduce((e,t,r,n)=>{if(/\p{Emoji}/u.test(t)){let i=n[r+1];i&&/\p{Emoji_Modifier}|\u200D/u.test(i)?(e.push(t+i),n[r+1]=""):e.push(t)}else/\p{Regional_Indicator}{2}/u.test(t+(n[r+1]||""))?e.push(t+n[r+1]):r>0&&/\p{Mn}|\p{Me}|\p{Mc}/u.test(t)?e[e.length-1]+=t:t&&e.push(t);return e},[])}let c="auto",u="inherit",f="justify",p="left",g="middle",m="normal",v="none",y=["direction","fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],b=y.length;function x(){return n||(n=i.Util.createCanvasElement().getContext("2d"))}class w extends o.Shape{constructor(e){super(function(e){return(e=e||{}).fillLinearGradientColorStops||e.fillRadialGradientColorStops||e.fillPatternImage||(e.fill=e.fill||"black"),e}(e)),this._partialTextX=0,this._partialTextY=0;for(let e=0;e<b;e++)this.on(y[e]+"Change.konva",this._setTextData);this._setTextData()}_sceneFunc(e){let t=this.textArr,r=t.length;if(!this.text())return;let n=this.padding(),i=this.fontSize(),a=this.lineHeight()*i,o=this.verticalAlign(),l=this.direction(),d=0,c=this.align(),m=this.getWidth(),v=this.letterSpacing(),y=this.fill(),b=this.textDecoration(),x=-1!==b.indexOf("underline"),w=-1!==b.indexOf("line-through"),_;l=l===u?e.direction:l;let S=a/2,C=g;if(s.Konva._fixTextRendering){let e=this.measureSize("M");C="alphabetic",S=(e.fontBoundingBoxAscent-e.fontBoundingBoxDescent)/2+a/2}for("rtl"===l&&e.setAttr("direction",l),e.setAttr("font",this._getContextFont()),e.setAttr("textBaseline",C),e.setAttr("textAlign",p),o===g?d=(this.getHeight()-r*a-2*n)/2:"bottom"===o&&(d=this.getHeight()-r*a-2*n),e.translate(n,d+n),_=0;_<r;_++){let o=0,d=t[_],u=d.text,p=d.width,g=d.lastInParagraph;if(e.save(),"right"===c?o+=m-p-2*n:"center"===c&&(o+=(m-p-2*n)/2),x){e.save(),e.beginPath();let t=s.Konva._fixTextRendering?Math.round(i/4):Math.round(i/2),r=o,a=S+0+t;e.moveTo(r,a);let l=c!==f||g?p:m-2*n;e.lineTo(r+Math.round(l),a),e.lineWidth=i/15,e.strokeStyle=this._getLinearGradient()||y,e.stroke(),e.restore()}if(w){e.save(),e.beginPath();let t=s.Konva._fixTextRendering?-Math.round(i/4):0;e.moveTo(o,S+0+t);let r=c!==f||g?p:m-2*n;e.lineTo(o+Math.round(r),S+0+t),e.lineWidth=i/15,e.strokeStyle=this._getLinearGradient()||y,e.stroke(),e.restore()}if("rtl"!==l&&(0!==v||c===f)){let t=u.split(" ").length-1,r=h(u);for(let i=0;i<r.length;i++){let a=r[i];" "!==a||g||c!==f||(o+=(m-2*n-p)/t),this._partialTextX=o,this._partialTextY=S+0,this._partialText=a,e.fillStrokeShape(this),o+=this.measureSize(a).width+v}}else 0!==v&&e.setAttr("letterSpacing",`${v}px`),this._partialTextX=o,this._partialTextY=S+0,this._partialText=u,e.fillStrokeShape(this);e.restore(),r>1&&(S+=a)}}_hitFunc(e){let t=this.getWidth(),r=this.getHeight();e.beginPath(),e.rect(0,0,t,r),e.closePath(),e.fillStrokeShape(this)}setText(e){let t=i.Util._isString(e)?e:null==e?"":e+"";return this._setAttr("text",t),this}getWidth(){return this.attrs.width===c||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width}getHeight(){return this.attrs.height===c||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return i.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(e){var t,r,n,i,a,o,s,l,d,h,c;let u=x(),f=this.fontSize(),p;u.save(),u.font=this._getContextFont(),p=u.measureText(e),u.restore();let g=f/100;return{actualBoundingBoxAscent:null!=(t=p.actualBoundingBoxAscent)?t:71.58203125*g,actualBoundingBoxDescent:null!=(r=p.actualBoundingBoxDescent)?r:0,actualBoundingBoxLeft:null!=(n=p.actualBoundingBoxLeft)?n:-7.421875*g,actualBoundingBoxRight:null!=(i=p.actualBoundingBoxRight)?i:75.732421875*g,alphabeticBaseline:null!=(a=p.alphabeticBaseline)?a:0,emHeightAscent:null!=(o=p.emHeightAscent)?o:100*g,emHeightDescent:null!=(s=p.emHeightDescent)?s:-20*g,fontBoundingBoxAscent:null!=(l=p.fontBoundingBoxAscent)?l:91*g,fontBoundingBoxDescent:null!=(d=p.fontBoundingBoxDescent)?d:21*g,hangingBaseline:null!=(h=p.hangingBaseline)?h:72.80000305175781*g,ideographicBaseline:null!=(c=p.ideographicBaseline)?c:-21*g,width:p.width,height:f}}_getContextFont(){return this.fontStyle()+" "+this.fontVariant()+" "+this.fontSize()+"px "+this.fontFamily().split(",").map(e=>{let t=(e=e.trim()).indexOf(" ")>=0,r=e.indexOf('"')>=0||e.indexOf("'")>=0;return t&&!r&&(e=`"${e}"`),e}).join(", ")}_addTextLine(e){this.align()===f&&(e=e.trim());let t=this._getTextWidth(e);return this.textArr.push({text:e,width:t,lastInParagraph:!1})}_getTextWidth(e){let t=this.letterSpacing(),r=e.length;return x().measureText(e).width+t*r}_setTextData(){let e=this.text().split("\n"),t=+this.fontSize(),r=0,n=this.lineHeight()*t,i=this.attrs.width,a=this.attrs.height,o=i!==c&&void 0!==i,s=a!==c&&void 0!==a,l=this.padding(),d=i-2*l,u=a-2*l,f=0,p=this.wrap(),g="char"!==p&&p!==v,m=this.ellipsis();this.textArr=[],x().font=this._getContextFont();let y=m?this._getTextWidth("…"):0;for(let t=0,i=e.length;t<i;++t){let a=e[t],l=this._getTextWidth(a);if(o&&l>d)for(;a.length>0;){let e=0,t=h(a).length,i="",o=0;for(;e<t;){let r=e+t>>>1,l=h(a).slice(0,r+1).join(""),c=this._getTextWidth(l);(m&&s&&f+n>u?c+y:c)<=d?(e=r+1,i=l,o=c):t=r}if(i){if(g){let t,r=h(a),n=h(i),s=r[n.length];(t=(" "===s||"-"===s)&&o<=d?n.length:Math.max(n.lastIndexOf(" "),n.lastIndexOf("-"))+1)>0&&(e=t,i=r.slice(0,e).join(""),o=this._getTextWidth(i))}if(i=i.trimRight(),this._addTextLine(i),r=Math.max(r,o),f+=n,this._shouldHandleEllipsis(f)){this._tryToAddEllipsisToLastLine();break}if((a=h(a).slice(e).join("").trimLeft()).length>0&&(l=this._getTextWidth(a))<=d){this._addTextLine(a),f+=n,r=Math.max(r,l);break}}else break}else this._addTextLine(a),f+=n,r=Math.max(r,l),this._shouldHandleEllipsis(f)&&t<i-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),s&&f+n>u)break}this.textHeight=t,this.textWidth=r}_shouldHandleEllipsis(e){let t=+this.fontSize(),r=this.lineHeight()*t,n=this.attrs.height,i=this.padding();return this.wrap()===v||n!==c&&void 0!==n&&e+r>n-2*i}_tryToAddEllipsisToLastLine(){let e=this.attrs.width,t=this.padding(),r=this.ellipsis(),n=this.textArr[this.textArr.length-1];n&&r&&(e!==c&&void 0!==e&&(this._getTextWidth(n.text+"…")<e-2*t||(n.text=n.text.slice(0,n.text.length-3))),this.textArr.splice(this.textArr.length-1,1),this._addTextLine(n.text+"…"))}getStrokeScaleEnabled(){return!0}_useBufferCanvas(){let e=-1!==this.textDecoration().indexOf("underline")||-1!==this.textDecoration().indexOf("line-through"),t=this.hasShadow();return!!e&&!!t||super._useBufferCanvas()}}t.Text=w,w.prototype._fillFunc=function(e){e.fillText(this._partialText,this._partialTextX,this._partialTextY)},w.prototype._strokeFunc=function(e){e.setAttr("miterLimit",2),e.strokeText(this._partialText,this._partialTextX,this._partialTextY)},w.prototype.className="Text",w.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"],(0,d._registerNode)(w),a.Factory.overWriteSetter(w,"width",(0,l.getNumberOrAutoValidator)()),a.Factory.overWriteSetter(w,"height",(0,l.getNumberOrAutoValidator)()),a.Factory.addGetterSetter(w,"direction",u),a.Factory.addGetterSetter(w,"fontFamily","Arial"),a.Factory.addGetterSetter(w,"fontSize",12,(0,l.getNumberValidator)()),a.Factory.addGetterSetter(w,"fontStyle",m),a.Factory.addGetterSetter(w,"fontVariant",m),a.Factory.addGetterSetter(w,"padding",0,(0,l.getNumberValidator)()),a.Factory.addGetterSetter(w,"align",p),a.Factory.addGetterSetter(w,"verticalAlign","top"),a.Factory.addGetterSetter(w,"lineHeight",1,(0,l.getNumberValidator)()),a.Factory.addGetterSetter(w,"wrap","word"),a.Factory.addGetterSetter(w,"ellipsis",!1,(0,l.getBooleanValidator)()),a.Factory.addGetterSetter(w,"letterSpacing",0,(0,l.getNumberValidator)()),a.Factory.addGetterSetter(w,"text","",(0,l.getStringValidator)()),a.Factory.addGetterSetter(w,"textDecoration","")},3306:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),t.Shape=t.shapes=void 0;let i=r(6878),a=r(6509),o=r(8949),s=r(913),l=r(6394),d=r(6878),h=r(4555),c="hasShadow",u="shadowRGBA",f="patternImage",p="linearGradient",g="radialGradient";function m(){return n||(n=a.Util.createCanvasElement().getContext("2d"))}t.shapes={};class v extends s.Node{constructor(e){let r;for(super(e);!(r=a.Util.getRandomColor())||r in t.shapes;);this.colorKey=r,t.shapes[r]=this}getContext(){return a.Util.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return a.Util.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(c,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(f,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){let e=m().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(e&&e.setTransform){let t=new a.Transform;t.translate(this.fillPatternX(),this.fillPatternY()),t.rotate(i.Konva.getAngle(this.fillPatternRotation())),t.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),t.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());let r=t.getMatrix(),n="undefined"==typeof DOMMatrix?{a:r[0],b:r[1],c:r[2],d:r[3],e:r[4],f:r[5]}:new DOMMatrix(r);e.setTransform(n)}return e}}_getLinearGradient(){return this._getCache(p,this.__getLinearGradient)}__getLinearGradient(){let e=this.fillLinearGradientColorStops();if(e){let t=m(),r=this.fillLinearGradientStartPoint(),n=this.fillLinearGradientEndPoint(),i=t.createLinearGradient(r.x,r.y,n.x,n.y);for(let t=0;t<e.length;t+=2)i.addColorStop(e[t],e[t+1]);return i}}_getRadialGradient(){return this._getCache(g,this.__getRadialGradient)}__getRadialGradient(){let e=this.fillRadialGradientColorStops();if(e){let t=m(),r=this.fillRadialGradientStartPoint(),n=this.fillRadialGradientEndPoint(),i=t.createRadialGradient(r.x,r.y,this.fillRadialGradientStartRadius(),n.x,n.y,this.fillRadialGradientEndRadius());for(let t=0;t<e.length;t+=2)i.addColorStop(e[t],e[t+1]);return i}}getShadowRGBA(){return this._getCache(u,this._getShadowRGBA)}_getShadowRGBA(){if(!this.hasShadow())return;let e=a.Util.colorToRGBA(this.shadowColor());if(e)return"rgba("+e.r+","+e.g+","+e.b+","+e.a*(this.shadowOpacity()||1)+")"}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops()))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],()=>this.strokeEnabled()&&this.strokeWidth()&&!!(this.stroke()||this.strokeLinearGradientColorStops()))}hasHitStroke(){let e=this.hitStrokeWidth();return"auto"===e?this.hasStroke():this.strokeEnabled()&&!!e}intersects(e){let t=this.getStage();if(!t)return!1;let r=t.bufferHitCanvas;return r.getContext().clear(),this.drawHit(r,void 0,!0),r.context.getImageData(Math.round(e.x),Math.round(e.y),1,1).data[3]>0}destroy(){return s.Node.prototype.destroy.call(this),delete t.shapes[this.colorKey],delete this.colorKey,this}_useBufferCanvas(e){var t;if(!(null==(t=this.attrs.perfectDrawEnabled)||t))return!1;let r=e||this.hasFill(),n=this.hasStroke(),i=1!==this.getAbsoluteOpacity();if(r&&n&&i)return!0;let a=this.hasShadow(),o=this.shadowForStrokeEnabled();return!!r&&!!n&&!!a&&!!o}setStrokeHitEnabled(e){a.Util.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),e?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return 0!==this.hitStrokeWidth()}getSelfRect(){let e=this.size();return{x:this._centroid?-e.width/2:0,y:this._centroid?-e.height/2:0,width:e.width,height:e.height}}getClientRect(e={}){let t=!1,r=this.getParent();for(;r;){if(r.isCached()){t=!0;break}r=r.getParent()}let n=e.skipTransform,i=e.relativeTo||t&&this.getStage()||void 0,a=this.getSelfRect(),o=!e.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,s=a.width+o,l=a.height+o,d=!e.skipShadow&&this.hasShadow(),h=d?this.shadowOffsetX():0,c=d?this.shadowOffsetY():0,u=s+Math.abs(h),f=l+Math.abs(c),p=d&&this.shadowBlur()||0,g={width:u+2*p,height:f+2*p,x:-(o/2+p)+Math.min(h,0)+a.x,y:-(o/2+p)+Math.min(c,0)+a.y};return n?g:this._transformedRect(g,i)}drawScene(e,t,r){let n,i=this.getLayer(),a=(e||i.getCanvas()).getContext(),o=this._getCanvasCache(),s=this.getSceneFunc(),l=this.hasShadow(),d=t===this;if(!this.isVisible()&&!d)return this;if(o){a.save();let e=this.getAbsoluteTransform(t).getMatrix();return a.transform(e[0],e[1],e[2],e[3],e[4],e[5]),this._drawCachedSceneCanvas(a),a.restore(),this}if(!s)return this;if(a.save(),this._useBufferCanvas()&&1){n=this.getStage();let e=r||n.bufferCanvas,i=e.getContext();i.clear(),i.save(),i._applyLineJoin(this);let o=this.getAbsoluteTransform(t).getMatrix();i.transform(o[0],o[1],o[2],o[3],o[4],o[5]),s.call(this,i,this),i.restore();let d=e.pixelRatio;l&&a._applyShadow(this),a._applyOpacity(this),a._applyGlobalCompositeOperation(this),a.drawImage(e._canvas,e.x||0,e.y||0,e.width/d,e.height/d)}else{if(a._applyLineJoin(this),!d){let e=this.getAbsoluteTransform(t).getMatrix();a.transform(e[0],e[1],e[2],e[3],e[4],e[5]),a._applyOpacity(this),a._applyGlobalCompositeOperation(this)}l&&a._applyShadow(this),s.call(this,a,this)}return a.restore(),this}drawHit(e,t,r=!1){if(!this.shouldDrawHit(t,r))return this;let n=this.getLayer(),i=e||n.hitCanvas,o=i&&i.getContext(),s=this.hitFunc()||this.sceneFunc(),l=this._getCanvasCache(),d=l&&l.hit;if(this.colorKey||a.Util.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),d){o.save();let e=this.getAbsoluteTransform(t).getMatrix();return o.transform(e[0],e[1],e[2],e[3],e[4],e[5]),this._drawCachedHitCanvas(o),o.restore(),this}if(!s)return this;if(o.save(),o._applyLineJoin(this),this!==t){let e=this.getAbsoluteTransform(t).getMatrix();o.transform(e[0],e[1],e[2],e[3],e[4],e[5])}return s.call(this,o,this),o.restore(),this}drawHitFromCache(e=0){let t=this._getCanvasCache(),r=this._getCachedSceneCanvas(),n=t.hit,i=n.getContext(),o=n.getWidth(),s=n.getHeight();i.clear(),i.drawImage(r._canvas,0,0,o,s);try{let t=i.getImageData(0,0,o,s),r=t.data,n=r.length,l=a.Util._hexToRgb(this.colorKey);for(let t=0;t<n;t+=4)r[t+3]>e?(r[t]=l.r,r[t+1]=l.g,r[t+2]=l.b,r[t+3]=255):r[t+3]=0;i.putImageData(t,0,0)}catch(e){a.Util.error("Unable to draw hit graph from cached scene canvas. "+e.message)}return this}hasPointerCapture(e){return h.hasPointerCapture(e,this)}setPointerCapture(e){h.setPointerCapture(e,this)}releaseCapture(e){h.releaseCapture(e,this)}}t.Shape=v,v.prototype._fillFunc=function(e){let t=this.attrs.fillRule;t?e.fill(t):e.fill()},v.prototype._strokeFunc=function(e){e.stroke()},v.prototype._fillFuncHit=function(e){let t=this.attrs.fillRule;t?e.fill(t):e.fill()},v.prototype._strokeFuncHit=function(e){e.stroke()},v.prototype._centroid=!1,v.prototype.nodeType="Shape",(0,d._registerNode)(v),v.prototype.eventListeners={},v.prototype.on.call(v.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",function(){this._clearCache(c)}),v.prototype.on.call(v.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",function(){this._clearCache(u)}),v.prototype.on.call(v.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",function(){this._clearCache(f)}),v.prototype.on.call(v.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",function(){this._clearCache(p)}),v.prototype.on.call(v.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",function(){this._clearCache(g)}),o.Factory.addGetterSetter(v,"stroke",void 0,(0,l.getStringOrGradientValidator)()),o.Factory.addGetterSetter(v,"strokeWidth",2,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillAfterStrokeEnabled",!1),o.Factory.addGetterSetter(v,"hitStrokeWidth","auto",(0,l.getNumberOrAutoValidator)()),o.Factory.addGetterSetter(v,"strokeHitEnabled",!0,(0,l.getBooleanValidator)()),o.Factory.addGetterSetter(v,"perfectDrawEnabled",!0,(0,l.getBooleanValidator)()),o.Factory.addGetterSetter(v,"shadowForStrokeEnabled",!0,(0,l.getBooleanValidator)()),o.Factory.addGetterSetter(v,"lineJoin"),o.Factory.addGetterSetter(v,"lineCap"),o.Factory.addGetterSetter(v,"sceneFunc"),o.Factory.addGetterSetter(v,"hitFunc"),o.Factory.addGetterSetter(v,"dash"),o.Factory.addGetterSetter(v,"dashOffset",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"shadowColor",void 0,(0,l.getStringValidator)()),o.Factory.addGetterSetter(v,"shadowBlur",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"shadowOpacity",1,(0,l.getNumberValidator)()),o.Factory.addComponentsGetterSetter(v,"shadowOffset",["x","y"]),o.Factory.addGetterSetter(v,"shadowOffsetX",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"shadowOffsetY",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillPatternImage"),o.Factory.addGetterSetter(v,"fill",void 0,(0,l.getStringOrGradientValidator)()),o.Factory.addGetterSetter(v,"fillPatternX",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillPatternY",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillLinearGradientColorStops"),o.Factory.addGetterSetter(v,"strokeLinearGradientColorStops"),o.Factory.addGetterSetter(v,"fillRadialGradientStartRadius",0),o.Factory.addGetterSetter(v,"fillRadialGradientEndRadius",0),o.Factory.addGetterSetter(v,"fillRadialGradientColorStops"),o.Factory.addGetterSetter(v,"fillPatternRepeat","repeat"),o.Factory.addGetterSetter(v,"fillEnabled",!0),o.Factory.addGetterSetter(v,"strokeEnabled",!0),o.Factory.addGetterSetter(v,"shadowEnabled",!0),o.Factory.addGetterSetter(v,"dashEnabled",!0),o.Factory.addGetterSetter(v,"strokeScaleEnabled",!0),o.Factory.addGetterSetter(v,"fillPriority","color"),o.Factory.addComponentsGetterSetter(v,"fillPatternOffset",["x","y"]),o.Factory.addGetterSetter(v,"fillPatternOffsetX",0,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillPatternOffsetY",0,(0,l.getNumberValidator)()),o.Factory.addComponentsGetterSetter(v,"fillPatternScale",["x","y"]),o.Factory.addGetterSetter(v,"fillPatternScaleX",1,(0,l.getNumberValidator)()),o.Factory.addGetterSetter(v,"fillPatternScaleY",1,(0,l.getNumberValidator)()),o.Factory.addComponentsGetterSetter(v,"fillLinearGradientStartPoint",["x","y"]),o.Factory.addComponentsGetterSetter(v,"strokeLinearGradientStartPoint",["x","y"]),o.Factory.addGetterSetter(v,"fillLinearGradientStartPointX",0),o.Factory.addGetterSetter(v,"strokeLinearGradientStartPointX",0),o.Factory.addGetterSetter(v,"fillLinearGradientStartPointY",0),o.Factory.addGetterSetter(v,"strokeLinearGradientStartPointY",0),o.Factory.addComponentsGetterSetter(v,"fillLinearGradientEndPoint",["x","y"]),o.Factory.addComponentsGetterSetter(v,"strokeLinearGradientEndPoint",["x","y"]),o.Factory.addGetterSetter(v,"fillLinearGradientEndPointX",0),o.Factory.addGetterSetter(v,"strokeLinearGradientEndPointX",0),o.Factory.addGetterSetter(v,"fillLinearGradientEndPointY",0),o.Factory.addGetterSetter(v,"strokeLinearGradientEndPointY",0),o.Factory.addComponentsGetterSetter(v,"fillRadialGradientStartPoint",["x","y"]),o.Factory.addGetterSetter(v,"fillRadialGradientStartPointX",0),o.Factory.addGetterSetter(v,"fillRadialGradientStartPointY",0),o.Factory.addComponentsGetterSetter(v,"fillRadialGradientEndPoint",["x","y"]),o.Factory.addGetterSetter(v,"fillRadialGradientEndPointX",0),o.Factory.addGetterSetter(v,"fillRadialGradientEndPointY",0),o.Factory.addGetterSetter(v,"fillPatternRotation",0),o.Factory.addGetterSetter(v,"fillRule",void 0,(0,l.getStringValidator)()),o.Factory.backCompat(v,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"})},3311:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]])},3408:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("scissors",[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]])},3424:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Easings=t.Tween=void 0;let n=r(6509),i=r(6345),a=r(913),o=r(6878),s={node:1,duration:1,easing:1,onFinish:1,yoyo:1},l=["fill","stroke","shadowColor"],d=0;class h{constructor(e,t,r,n,i,a,o){this.prop=e,this.propFunc=t,this.begin=n,this._pos=n,this.duration=a,this._change=0,this.prevPos=0,this.yoyo=o,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=r,this._change=i-this.begin,this.pause()}fire(e){let t=this[e];t&&t()}setTime(e){e>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():e<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=e,this.update())}getTime(){return this._time}setPosition(e){this.prevPos=this._pos,this.propFunc(e),this._pos=e}getPosition(e){return void 0===e&&(e=this._time),this.func(e,this.begin,this._change,this.duration)}play(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(e){this.pause(),this._time=e,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){let e=this.getTimer()-this._startTime;2===this.state?this.setTime(e):3===this.state&&this.setTime(this.duration-e)}pause(){this.state=1,this.fire("onPause")}getTimer(){return new Date().getTime()}}class c{constructor(e){let r,a,l=this,u=e.node,f=u._id,p=e.easing||t.Easings.Linear,g=!!e.yoyo;r=void 0===e.duration?.3:0===e.duration?.001:e.duration,this.node=u,this._id=d++;let m=u.getLayer()||(u instanceof o.Konva.Stage?u.getLayers():null);for(a in m||n.Util.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new i.Animation(function(){l.tween.onEnterFrame()},m),this.tween=new h(a,function(e){l._tweenFunc(e)},p,0,1,1e3*r,g),this._addListeners(),c.attrs[f]||(c.attrs[f]={}),c.attrs[f][this._id]||(c.attrs[f][this._id]={}),c.tweens[f]||(c.tweens[f]={}),e)void 0===s[a]&&this._addAttr(a,e[a]);this.reset(),this.onFinish=e.onFinish,this.onReset=e.onReset,this.onUpdate=e.onUpdate}_addAttr(e,t){let r,i,a,o,s,d=this.node,h=d._id,u=c.tweens[h][e];u&&delete c.attrs[h][u][e];let f=d.getAttr(e);if(n.Util._isArray(t))if(r=[],i=Math.max(t.length,f.length),"points"===e&&t.length!==f.length&&(t.length>f.length?(o=f,f=n.Util._prepareArrayForTween(f,t,d.closed())):(a=t,t=n.Util._prepareArrayForTween(t,f,d.closed()))),0===e.indexOf("fill"))for(let e=0;e<i;e++)if(e%2==0)r.push(t[e]-f[e]);else{let i=n.Util.colorToRGBA(f[e]);s=n.Util.colorToRGBA(t[e]),f[e]=i,r.push({r:s.r-i.r,g:s.g-i.g,b:s.b-i.b,a:s.a-i.a})}else for(let e=0;e<i;e++)r.push(t[e]-f[e]);else -1!==l.indexOf(e)?(f=n.Util.colorToRGBA(f),r={r:(s=n.Util.colorToRGBA(t)).r-f.r,g:s.g-f.g,b:s.b-f.b,a:s.a-f.a}):r=t-f;c.attrs[h][this._id][e]={start:f,diff:r,end:t,trueEnd:a,trueStart:o},c.tweens[h][e]=this._id}_tweenFunc(e){let t,r,i,a,o,s,d,h,u=this.node,f=c.attrs[u._id][this._id];for(t in f){if(i=(r=f[t]).start,a=r.diff,h=r.end,n.Util._isArray(i))if(o=[],d=Math.max(i.length,h.length),0===t.indexOf("fill"))for(s=0;s<d;s++)s%2==0?o.push((i[s]||0)+a[s]*e):o.push("rgba("+Math.round(i[s].r+a[s].r*e)+","+Math.round(i[s].g+a[s].g*e)+","+Math.round(i[s].b+a[s].b*e)+","+(i[s].a+a[s].a*e)+")");else for(s=0;s<d;s++)o.push((i[s]||0)+a[s]*e);else o=-1!==l.indexOf(t)?"rgba("+Math.round(i.r+a.r*e)+","+Math.round(i.g+a.g*e)+","+Math.round(i.b+a.b*e)+","+(i.a+a.a*e)+")":i+a*e;u.setAttr(t,o)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{let e=this.node,t=c.attrs[e._id][this._id];t.points&&t.points.trueEnd&&e.setAttr("points",t.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{let e=this.node,t=c.attrs[e._id][this._id];t.points&&t.points.trueStart&&e.points(t.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(e){return this.tween.seek(1e3*e),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){let e=this.node._id,t=this._id,r=c.tweens[e];for(let t in this.pause(),this.anim&&this.anim.stop(),r)delete c.tweens[e][t];delete c.attrs[e][t],c.tweens[e]&&(0===Object.keys(c.tweens[e]).length&&delete c.tweens[e],0===Object.keys(c.attrs[e]).length&&delete c.attrs[e])}}t.Tween=c,c.attrs={},c.tweens={},a.Node.prototype.to=function(e){let t=e.onFinish;e.node=this,e.onFinish=function(){this.destroy(),t&&t()},new c(e).play()},t.Easings={BackEaseIn:(e,t,r,n)=>r*(e/=n)*e*(2.70158*e-1.70158)+t,BackEaseOut:(e,t,r,n)=>r*((e=e/n-1)*e*(2.70158*e+1.70158)+1)+t,BackEaseInOut(e,t,r,n){let i=1.70158;return(e/=n/2)<1?r/2*(e*e*(((i*=1.525)+1)*e-i))+t:r/2*((e-=2)*e*(((i*=1.525)+1)*e+i)+2)+t},ElasticEaseIn(e,t,r,n,i,a){let o=0;return 0===e?t:1==(e/=n)?t+r:(a||(a=.3*n),!i||i<Math.abs(r)?(i=r,o=a/4):o=a/(2*Math.PI)*Math.asin(r/i),-(i*Math.pow(2,10*(e-=1))*Math.sin(2*Math.PI*(e*n-o)/a))+t)},ElasticEaseOut(e,t,r,n,i,a){let o=0;return 0===e?t:1==(e/=n)?t+r:(a||(a=.3*n),!i||i<Math.abs(r)?(i=r,o=a/4):o=a/(2*Math.PI)*Math.asin(r/i),i*Math.pow(2,-10*e)*Math.sin(2*Math.PI*(e*n-o)/a)+r+t)},ElasticEaseInOut(e,t,r,n,i,a){let o=0;return 0===e?t:2==(e/=n/2)?t+r:(a||(a=.3*1.5*n),!i||i<Math.abs(r)?(i=r,o=a/4):o=a/(2*Math.PI)*Math.asin(r/i),e<1)?-.5*(i*Math.pow(2,10*(e-=1))*Math.sin(2*Math.PI*(e*n-o)/a))+t:i*Math.pow(2,-10*(e-=1))*Math.sin(2*Math.PI*(e*n-o)/a)*.5+r+t},BounceEaseOut:(e,t,r,n)=>(e/=n)<1/2.75?7.5625*e*e*r+t:e<2/2.75?r*(7.5625*(e-=1.5/2.75)*e+.75)+t:e<2.5/2.75?r*(7.5625*(e-=2.25/2.75)*e+.9375)+t:r*(7.5625*(e-=2.625/2.75)*e+.984375)+t,BounceEaseIn:(e,r,n,i)=>n-t.Easings.BounceEaseOut(i-e,0,n,i)+r,BounceEaseInOut:(e,r,n,i)=>e<i/2?.5*t.Easings.BounceEaseIn(2*e,0,n,i)+r:.5*t.Easings.BounceEaseOut(2*e-i,0,n,i)+.5*n+r,EaseIn:(e,t,r,n)=>r*(e/=n)*e+t,EaseOut:(e,t,r,n)=>-r*(e/=n)*(e-2)+t,EaseInOut:(e,t,r,n)=>(e/=n/2)<1?r/2*e*e+t:-r/2*(--e*(e-2)-1)+t,StrongEaseIn:(e,t,r,n)=>r*(e/=n)*e*e*e*e+t,StrongEaseOut:(e,t,r,n)=>r*((e=e/n-1)*e*e*e*e+1)+t,StrongEaseInOut:(e,t,r,n)=>(e/=n/2)<1?r/2*e*e*e*e*e+t:r/2*((e-=2)*e*e*e*e+2)+t,Linear:(e,t,r,n)=>r*e/n+t}},3500:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},3529:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Noise=void 0;let n=r(8949),i=r(913),a=r(6394);t.Noise=function(e){let t=255*this.noise(),r=e.data,n=r.length,i=t/2;for(let e=0;e<n;e+=4)r[e+0]+=i-2*i*Math.random(),r[e+1]+=i-2*i*Math.random(),r[e+2]+=i-2*i*Math.random()},n.Factory.addGetterSetter(i.Node,"noise",.2,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},3624:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Star=void 0;let n=r(8949),i=r(3306),a=r(6394),o=r(6878);class s extends i.Shape{_sceneFunc(e){let t=this.innerRadius(),r=this.outerRadius(),n=this.numPoints();e.beginPath(),e.moveTo(0,0-r);for(let i=1;i<2*n;i++){let a=i%2==0?r:t,o=a*Math.sin(i*Math.PI/n),s=-1*a*Math.cos(i*Math.PI/n);e.lineTo(o,s)}e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}}t.Star=s,s.prototype.className="Star",s.prototype._centroid=!0,s.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,o._registerNode)(s),n.Factory.addGetterSetter(s,"numPoints",5,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(s,"innerRadius",0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(s,"outerRadius",0,(0,a.getNumberValidator)())},3654:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},3655:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>s});var n=r(2115),i=r(7650),a=r(9708),o=r(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?r:t,{...a,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,r)=>{r.d(t,{A:()=>K});var n,i,a=function(){return(a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,r(2115)),l="right-scroll-bar-position",d="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,u=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i=(void 0===t&&(t=f),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var i=t(e,n);return r.push(i),function(){r=r.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var i=r;r=[],i.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(a)};o(),r={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),g=function(){},m=s.forwardRef(function(e,t){var r,n,i,l,d=s.useRef(null),f=s.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),m=f[0],v=f[1],y=e.forwardProps,b=e.children,x=e.className,w=e.removeScrollBar,_=e.enabled,S=e.shards,C=e.sideCar,k=e.noRelative,P=e.noIsolation,A=e.inert,M=e.allowPinchZoom,T=e.as,E=e.gapMode,R=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(r=[d,t],n=function(e){return r.forEach(function(t){return h(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:n,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=n,l=i.facade,c(function(){var e=u.get(l);if(e){var t=new Set(e),n=new Set(r),i=l.current;t.forEach(function(e){n.has(e)||h(e,null)}),n.forEach(function(e){t.has(e)||h(e,i)})}u.set(l,r)},[r]),l),N=a(a({},R),m);return s.createElement(s.Fragment,null,_&&s.createElement(C,{sideCar:p,removeScrollBar:w,shards:S,noRelative:k,noIsolation:P,inert:A,setCallbacks:v,allowPinchZoom:!!M,lockRef:d,gapMode:E}),y?s.cloneElement(s.Children.only(b),a(a({},N),{ref:D})):s.createElement(void 0===T?"div":T,a({},N,{className:x,ref:D}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:d,zeroRight:l};var v=function(e){var t=e.sideCar,r=o(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return s.createElement(n,a({},r))};v.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,o;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,r){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},x=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},_=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[_(r),_(n),_(i)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=S(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},k=x(),P="data-scroll-locked",A=function(e,t,r,n){var i=e.left,a=e.top,o=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(d," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(d," .").concat(d," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},T=function(){s.useEffect(function(){return document.body.setAttribute(P,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},E=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,i=void 0===n?"margin":n;T();var a=s.useMemo(function(){return C(i)},[i]);return s.createElement(k,{styles:A(a,!t,i,r?"":"!important")})},R=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return R=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){R=!1}var N=!!R&&{passive:!1},F=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},O=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),L(e,n)){var i=I(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},L=function(e,t){return"v"===e?F(t,"overflowY"):F(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},G=function(e,t,r,n,i){var a,o=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),s=o*n,l=r.target,d=t.contains(l),h=!1,c=s>0,u=0,f=0;do{if(!l)break;var p=I(e,l),g=p[0],m=p[1]-p[2]-o*g;(g||m)&&L(e,l)&&(u+=m,f+=g);var v=l.parentNode;l=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return c&&(i&&1>Math.abs(u)||!i&&s>u)?h=!0:!c&&(i&&1>Math.abs(f)||!i&&-s>f)&&(h=!0),h},j=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},U=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,H=[];let V=(n=function(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),i=s.useState(z++)[0],a=s.useState(x)[0],o=s.useRef(e);s.useEffect(function(){o.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,i=0,a=t.length;i<a;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var i,a=j(e),s=r.current,l="deltaX"in e?e.deltaX:s[0]-a[0],d="deltaY"in e?e.deltaY:s[1]-a[1],h=e.target,c=Math.abs(l)>Math.abs(d)?"h":"v";if("touches"in e&&"h"===c&&"range"===h.type)return!1;var u=O(c,h);if(!u)return!0;if(u?i=c:(i="v"===c?"h":"v",u=O(c,h)),!u)return!1;if(!n.current&&"changedTouches"in e&&(l||d)&&(n.current=i),!i)return!0;var f=n.current||i;return G(f,t,e,"h"===f?l:d,!0)},[]),d=s.useCallback(function(e){if(H.length&&H[H.length-1]===a){var r="deltaY"in e?U(e):j(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(o.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),h=s.useCallback(function(e,r,n,i){var a={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),c=s.useCallback(function(e){r.current=j(e),n.current=void 0},[]),u=s.useCallback(function(t){h(t.type,U(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){h(t.type,j(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return H.push(a),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:f}),document.addEventListener("wheel",d,N),document.addEventListener("touchmove",d,N),document.addEventListener("touchstart",c,N),function(){H=H.filter(function(e){return e!==a}),document.removeEventListener("wheel",d,N),document.removeEventListener("touchmove",d,N),document.removeEventListener("touchstart",c,N)}},[]);var p=e.removeScrollBar,g=e.inert;return s.createElement(s.Fragment,null,g?s.createElement(a,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(E,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(n),v);var W=s.forwardRef(function(e,t){return s.createElement(m,a({},e,{ref:t,sideCar:V}))});W.classNames=m.classNames;let K=W},3927:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Path=void 0;let n=r(8949),i=r(6878),a=r(3306),o=r(6659);class s extends a.Shape{constructor(e){super(e),this.dataArray=[],this.pathLength=0,this._readDataAttribute(),this.on("dataChange.konva",function(){this._readDataAttribute()})}_readDataAttribute(){this.dataArray=s.parsePathData(this.data()),this.pathLength=s.getPathLength(this.dataArray)}_sceneFunc(e){let t=this.dataArray;e.beginPath();let r=!1;for(let n=0;n<t.length;n++){let i=t[n].command,a=t[n].points;switch(i){case"L":e.lineTo(a[0],a[1]);break;case"M":e.moveTo(a[0],a[1]);break;case"C":e.bezierCurveTo(a[0],a[1],a[2],a[3],a[4],a[5]);break;case"Q":e.quadraticCurveTo(a[0],a[1],a[2],a[3]);break;case"A":let o=a[0],s=a[1],l=a[2],d=a[3],h=a[4],c=a[5],u=a[6],f=a[7],p=l>d?l:d,g=l>d?1:l/d,m=l>d?d/l:1;e.translate(o,s),e.rotate(u),e.scale(g,m),e.arc(0,0,p,h,h+c,1-f),e.scale(1/g,1/m),e.rotate(-u),e.translate(-o,-s);break;case"z":r=!0,e.closePath()}}r||this.hasFill()?e.fillStrokeShape(this):e.strokeShape(this)}getSelfRect(){let e,t,r=[];this.dataArray.forEach(function(e){if("A"===e.command){let t=e.points[4],n=e.points[5],i=e.points[4]+n,a=Math.PI/180;if(Math.abs(t-i)<a&&(a=Math.abs(t-i)),n<0)for(let n=t-a;n>i;n-=a){let t=s.getPointOnEllipticalArc(e.points[0],e.points[1],e.points[2],e.points[3],n,0);r.push(t.x,t.y)}else for(let n=t+a;n<i;n+=a){let t=s.getPointOnEllipticalArc(e.points[0],e.points[1],e.points[2],e.points[3],n,0);r.push(t.x,t.y)}}else if("C"===e.command)for(let t=0;t<=1;t+=.01){let n=s.getPointOnCubicBezier(t,e.start.x,e.start.y,e.points[0],e.points[1],e.points[2],e.points[3],e.points[4],e.points[5]);r.push(n.x,n.y)}else r=r.concat(e.points)});let n=r[0],i=r[0],a=r[1],o=r[1];for(let s=0;s<r.length/2;s++)e=r[2*s],t=r[2*s+1],isNaN(e)||(n=Math.min(n,e),i=Math.max(i,e)),isNaN(t)||(a=Math.min(a,t),o=Math.max(o,t));return{x:n,y:a,width:i-n,height:o-a}}getLength(){return this.pathLength}getPointAtLength(e){return s.getPointAtLengthOfDataArray(e,this.dataArray)}static getLineLength(e,t,r,n){return Math.sqrt((r-e)*(r-e)+(n-t)*(n-t))}static getPathLength(e){let t=0;for(let r=0;r<e.length;++r)t+=e[r].pathLength;return t}static getPointAtLengthOfDataArray(e,t){let r,n=0,i=t.length;if(!i)return null;for(;n<i&&e>t[n].pathLength;)e-=t[n].pathLength,++n;if(n===i)return{x:(r=t[n-1].points.slice(-2))[0],y:r[1]};if(e<.01)return"M"===t[n].command?{x:(r=t[n].points.slice(0,2))[0],y:r[1]}:{x:t[n].start.x,y:t[n].start.y};let a=t[n],l=a.points;switch(a.command){case"L":return s.getPointOnLine(e,a.start.x,a.start.y,l[0],l[1]);case"C":return s.getPointOnCubicBezier((0,o.t2length)(e,s.getPathLength(t),e=>(0,o.getCubicArcLength)([a.start.x,l[0],l[2],l[4]],[a.start.y,l[1],l[3],l[5]],e)),a.start.x,a.start.y,l[0],l[1],l[2],l[3],l[4],l[5]);case"Q":return s.getPointOnQuadraticBezier((0,o.t2length)(e,s.getPathLength(t),e=>(0,o.getQuadraticArcLength)([a.start.x,l[0],l[2]],[a.start.y,l[1],l[3]],e)),a.start.x,a.start.y,l[0],l[1],l[2],l[3]);case"A":let d=l[0],h=l[1],c=l[2],u=l[3],f=l[5],p=l[6],g=l[4];return g+=f*e/a.pathLength,s.getPointOnEllipticalArc(d,h,c,u,g,p)}return null}static getPointOnLine(e,t,r,n,i,a,o){a=null!=a?a:t,o=null!=o?o:r;let s=this.getLineLength(t,r,n,i);if(s<1e-10)return{x:t,y:r};if(n===t)return{x:a,y:o+(i>r?e:-e)};let l=(i-r)/(n-t),d=Math.sqrt(e*e/(1+l*l))*(n<t?-1:1);if(1e-10>Math.abs(o-r-l*(a-t)))return{x:a+d,y:o+l*d};let h=((a-t)*(n-t)+(o-r)*(i-r))/(s*s),c=t+h*(n-t),u=r+h*(i-r),f=this.getLineLength(a,o,c,u),p=Math.sqrt(e*e-f*f),g=Math.sqrt(p*p/(1+l*l))*(n<t?-1:1);return{x:c+g,y:u+l*g}}static getPointOnCubicBezier(e,t,r,n,i,a,o,s,l){function d(e){return 3*e*(1-e)*(1-e)}return{x:e*e*e*s+3*e*e*(1-e)*a+n*d(e)+(1-e)*(1-e)*(1-e)*t,y:e*e*e*l+3*e*e*(1-e)*o+i*d(e)+(1-e)*(1-e)*(1-e)*r}}static getPointOnQuadraticBezier(e,t,r,n,i,a,o){return{x:e*e*a+2*e*(1-e)*n+(1-e)*(1-e)*t,y:e*e*o+2*e*(1-e)*i+(1-e)*(1-e)*r}}static getPointOnEllipticalArc(e,t,r,n,i,a){let o=Math.cos(a),s=Math.sin(a),l={x:r*Math.cos(i),y:n*Math.sin(i)};return{x:e+(l.x*o-l.y*s),y:t+(l.x*s+l.y*o)}}static parsePathData(e){let t;if(!e)return[];let r=e,n=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];r=r.replace(RegExp(" ","g"),",");for(let e=0;e<n.length;e++)r=r.replace(RegExp(n[e],"g"),"|"+n[e]);let i=r.split("|"),a=[],o=[],s=0,l=0,d=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi;for(let e=1;e<i.length;e++){let r=i[e],n=r.charAt(0);for(r=r.slice(1),o.length=0;t=d.exec(r);)o.push(t[0]);let h=[];for(let e=0,t=o.length;e<t;e++){if("00"===o[e]){h.push(0,0);continue}let t=parseFloat(o[e]);isNaN(t)?h.push(0):h.push(t)}for(;h.length>0;){let e,t,r,i,o,d,c,u,f,p;if(isNaN(h[0]))break;let g="",m=[],v=s,y=l;switch(n){case"l":s+=h.shift(),l+=h.shift(),g="L",m.push(s,l);break;case"L":s=h.shift(),l=h.shift(),m.push(s,l);break;case"m":let b=h.shift(),x=h.shift();if(s+=b,l+=x,g="M",a.length>2&&"z"===a[a.length-1].command){for(let e=a.length-2;e>=0;e--)if("M"===a[e].command){s=a[e].points[0]+b,l=a[e].points[1]+x;break}}m.push(s,l),n="l";break;case"M":s=h.shift(),l=h.shift(),g="M",m.push(s,l),n="L";break;case"h":s+=h.shift(),g="L",m.push(s,l);break;case"H":s=h.shift(),g="L",m.push(s,l);break;case"v":l+=h.shift(),g="L",m.push(s,l);break;case"V":l=h.shift(),g="L",m.push(s,l);break;case"C":m.push(h.shift(),h.shift(),h.shift(),h.shift()),s=h.shift(),l=h.shift(),m.push(s,l);break;case"c":m.push(s+h.shift(),l+h.shift(),s+h.shift(),l+h.shift()),s+=h.shift(),l+=h.shift(),g="C",m.push(s,l);break;case"S":t=s,r=l,"C"===(e=a[a.length-1]).command&&(t=s+(s-e.points[2]),r=l+(l-e.points[3])),m.push(t,r,h.shift(),h.shift()),s=h.shift(),l=h.shift(),g="C",m.push(s,l);break;case"s":t=s,r=l,"C"===(e=a[a.length-1]).command&&(t=s+(s-e.points[2]),r=l+(l-e.points[3])),m.push(t,r,s+h.shift(),l+h.shift()),s+=h.shift(),l+=h.shift(),g="C",m.push(s,l);break;case"Q":m.push(h.shift(),h.shift()),s=h.shift(),l=h.shift(),m.push(s,l);break;case"q":m.push(s+h.shift(),l+h.shift()),s+=h.shift(),l+=h.shift(),g="Q",m.push(s,l);break;case"T":t=s,r=l,"Q"===(e=a[a.length-1]).command&&(t=s+(s-e.points[0]),r=l+(l-e.points[1])),s=h.shift(),l=h.shift(),g="Q",m.push(t,r,s,l);break;case"t":t=s,r=l,"Q"===(e=a[a.length-1]).command&&(t=s+(s-e.points[0]),r=l+(l-e.points[1])),s+=h.shift(),l+=h.shift(),g="Q",m.push(t,r,s,l);break;case"A":i=h.shift(),o=h.shift(),d=h.shift(),c=h.shift(),u=h.shift(),f=s,p=l,s=h.shift(),l=h.shift(),g="A",m=this.convertEndpointToCenterParameterization(f,p,s,l,c,u,i,o,d);break;case"a":i=h.shift(),o=h.shift(),d=h.shift(),c=h.shift(),u=h.shift(),f=s,p=l,s+=h.shift(),l+=h.shift(),g="A",m=this.convertEndpointToCenterParameterization(f,p,s,l,c,u,i,o,d)}a.push({command:g||n,points:m,start:{x:v,y:y},pathLength:this.calcLength(v,y,g||n,m)})}("z"===n||"Z"===n)&&a.push({command:"z",points:[],start:void 0,pathLength:0})}return a}static calcLength(e,t,r,n){let i,a,l,d;switch(r){case"L":return s.getLineLength(e,t,n[0],n[1]);case"C":return(0,o.getCubicArcLength)([e,n[0],n[2],n[4]],[t,n[1],n[3],n[5]],1);case"Q":return(0,o.getQuadraticArcLength)([e,n[0],n[2]],[t,n[1],n[3]],1);case"A":i=0;let h=n[4],c=n[5],u=n[4]+c,f=Math.PI/180;if(Math.abs(h-u)<f&&(f=Math.abs(h-u)),a=s.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],h,0),c<0)for(d=h-f;d>u;d-=f)l=s.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],d,0),i+=s.getLineLength(a.x,a.y,l.x,l.y),a=l;else for(d=h+f;d<u;d+=f)l=s.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],d,0),i+=s.getLineLength(a.x,a.y,l.x,l.y),a=l;return l=s.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),i+=s.getLineLength(a.x,a.y,l.x,l.y)}return 0}static convertEndpointToCenterParameterization(e,t,r,n,i,a,o,s,l){let d=Math.PI/180*l,h=Math.cos(d)*(e-r)/2+Math.sin(d)*(t-n)/2,c=-1*Math.sin(d)*(e-r)/2+Math.cos(d)*(t-n)/2,u=h*h/(o*o)+c*c/(s*s);u>1&&(o*=Math.sqrt(u),s*=Math.sqrt(u));let f=Math.sqrt((o*o*(s*s)-o*o*(c*c)-s*s*(h*h))/(o*o*(c*c)+s*s*(h*h)));i===a&&(f*=-1),isNaN(f)&&(f=0);let p=f*o*c/s,g=-(f*s)*h/o,m=(e+r)/2+Math.cos(d)*p-Math.sin(d)*g,v=(t+n)/2+Math.sin(d)*p+Math.cos(d)*g,y=function(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])},b=function(e,t){return(e[0]*t[0]+e[1]*t[1])/(y(e)*y(t))},x=function(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(b(e,t))},w=x([1,0],[(h-p)/o,(c-g)/s]),_=[(h-p)/o,(c-g)/s],S=[(-1*h-p)/o,(-1*c-g)/s],C=x(_,S);return -1>=b(_,S)&&(C=Math.PI),b(_,S)>=1&&(C=0),0===a&&C>0&&(C-=2*Math.PI),1===a&&C<0&&(C+=2*Math.PI),[m,v,o,s,w,C,d,a]}}t.Path=s,s.prototype.className="Path",s.prototype._attrsAffectingSize=["data"],(0,i._registerNode)(s),n.Factory.addGetterSetter(s,"data")},4011:(e,t,r)=>{r.d(t,{H4:()=>C,_V:()=>S,bL:()=>_});var n=r(2115),i=r(6081),a=r(9033),o=r(2712),s=r(3655),l=r(1414);function d(){return()=>{}}var h=r(5155),c="Avatar",[u,f]=(0,i.A)(c),[p,g]=u(c),m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[a,o]=n.useState("idle");return(0,h.jsx)(p,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:o,children:(0,h.jsx)(s.sG.span,{...i,ref:t})})});m.displayName=c;var v="AvatarImage",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:c=()=>{},...u}=e,f=g(v,r),p=function(e,t){let{referrerPolicy:r,crossOrigin:i}=t,a=(0,l.useSyncExternalStore)(d,()=>!0,()=>!1),s=n.useRef(null),h=a?(s.current||(s.current=new window.Image),s.current):null,[c,u]=n.useState(()=>w(h,e));return(0,o.N)(()=>{u(w(h,e))},[h,e]),(0,o.N)(()=>{let e=e=>()=>{u(e)};if(!h)return;let t=e("loaded"),n=e("error");return h.addEventListener("load",t),h.addEventListener("error",n),r&&(h.referrerPolicy=r),"string"==typeof i&&(h.crossOrigin=i),()=>{h.removeEventListener("load",t),h.removeEventListener("error",n)}},[h,i,r]),c}(i,u),m=(0,a.c)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,h.jsx)(s.sG.img,{...u,ref:t,src:i}):null});y.displayName=v;var b="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...a}=e,o=g(b,r),[l,d]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>d(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==o.imageLoadingStatus?(0,h.jsx)(s.sG.span,{...a,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=b;var _=m,S=y,C=x},4073:(e,t,r)=>{r.d(t,{CC:()=>H,Q6:()=>V,bL:()=>z,zi:()=>W});var n=r(2115),i=r(9367),a=r(5185),o=r(6101),s=r(6081),l=r(5845),d=r(4315),h=r(5503),c=r(1275),u=r(3655),f=r(7328),p=r(5155),g=["PageUp","PageDown"],m=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,x,w]=(0,f.N)(y),[_,S]=(0,s.A)(y,[w]),[C,k]=_(y),P=n.forwardRef((e,t)=>{let{name:r,min:o=0,max:s=100,step:d=1,orientation:h="horizontal",disabled:c=!1,minStepsBetweenThumbs:u=0,defaultValue:f=[o],value:v,onValueChange:y=()=>{},onValueCommit:x=()=>{},inverted:w=!1,form:_,...S}=e,k=n.useRef(new Set),P=n.useRef(0),A="horizontal"===h,[M=[],R]=(0,l.i)({prop:v,defaultProp:f,onChange:e=>{var t;null==(t=[...k.current][P.current])||t.focus(),y(e)}}),D=n.useRef(M);function N(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},n=(String(d).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-o)/d)*d+o,n),l=(0,i.q)(a,[o,s]);R(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,l,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,u*d))return e;{P.current=n.indexOf(l);let t=String(n)!==String(e);return t&&r&&x(n),t?n:e}})}return(0,p.jsx)(C,{scope:e.__scopeSlider,name:r,disabled:c,min:o,max:s,valueIndexToChangeRef:P,thumbs:k.current,values:M,orientation:h,form:_,children:(0,p.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(A?T:E,{"aria-disabled":c,"data-disabled":c?"":void 0,...S,ref:t,onPointerDown:(0,a.m)(S.onPointerDown,()=>{c||(D.current=M)}),min:o,max:s,inverted:w,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(M,e);N(e,t)},onSlideMove:c?void 0:function(e){N(e,P.current)},onSlideEnd:c?void 0:function(){let e=D.current[P.current];M[P.current]!==e&&x(M)},onHomeKeyDown:()=>!c&&N(o,0,{commit:!0}),onEndKeyDown:()=>!c&&N(s,M.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!c){let e=g.includes(t.key)||t.shiftKey&&m.includes(t.key),n=P.current;N(M[n]+d*(e?10:1)*r,n,{commit:!0})}}})})})})});P.displayName=y;var[A,M]=_(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),T=n.forwardRef((e,t)=>{let{min:r,max:i,dir:a,inverted:s,onSlideStart:l,onSlideMove:h,onSlideEnd:c,onStepKeyDown:u,...f}=e,[g,m]=n.useState(null),y=(0,o.s)(t,e=>m(e)),b=n.useRef(void 0),x=(0,d.jH)(a),w="ltr"===x,_=w&&!s||!w&&s;function S(e){let t=b.current||g.getBoundingClientRect(),n=B([0,t.width],_?[r,i]:[i,r]);return b.current=t,n(e-t.left)}return(0,p.jsx)(A,{scope:e.__scopeSlider,startEdge:_?"left":"right",endEdge:_?"right":"left",direction:_?1:-1,size:"width",children:(0,p.jsx)(R,{dir:x,"data-orientation":"horizontal",...f,ref:y,style:{...f.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=S(e.clientX);null==l||l(t)},onSlideMove:e=>{let t=S(e.clientX);null==h||h(t)},onSlideEnd:()=>{b.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=v[_?"from-left":"from-right"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),E=n.forwardRef((e,t)=>{let{min:r,max:i,inverted:a,onSlideStart:s,onSlideMove:l,onSlideEnd:d,onStepKeyDown:h,...c}=e,u=n.useRef(null),f=(0,o.s)(t,u),g=n.useRef(void 0),m=!a;function y(e){let t=g.current||u.current.getBoundingClientRect(),n=B([0,t.height],m?[i,r]:[r,i]);return g.current=t,n(e-t.top)}return(0,p.jsx)(A,{scope:e.__scopeSlider,startEdge:m?"bottom":"top",endEdge:m?"top":"bottom",size:"height",direction:m?1:-1,children:(0,p.jsx)(R,{"data-orientation":"vertical",...c,ref:f,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);null==s||s(t)},onSlideMove:e=>{let t=y(e.clientY);null==l||l(t)},onSlideEnd:()=>{g.current=void 0,null==d||d()},onStepKeyDown:e=>{let t=v[m?"from-bottom":"from-top"].includes(e.key);null==h||h({event:e,direction:t?-1:1})}})})}),R=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:i,onSlideEnd:o,onHomeKeyDown:s,onEndKeyDown:l,onStepKeyDown:d,...h}=e,c=k(y,r);return(0,p.jsx)(u.sG.span,{...h,ref:t,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Home"===e.key?(s(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):g.concat(m).includes(e.key)&&(d(e),e.preventDefault())}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),o(e))})})}),D="SliderTrack",N=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,i=k(D,r);return(0,p.jsx)(u.sG.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...n,ref:t})});N.displayName=D;var F="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:r,...i}=e,a=k(F,r),s=M(F,r),l=n.useRef(null),d=(0,o.s)(t,l),h=a.values.length,c=a.values.map(e=>U(e,a.min,a.max)),f=h>1?Math.min(...c):0,g=100-Math.max(...c);return(0,p.jsx)(u.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...i,ref:d,style:{...e.style,[s.startEdge]:f+"%",[s.endEdge]:g+"%"}})});O.displayName=F;var L="SliderThumb",I=n.forwardRef((e,t)=>{let r=x(e.__scopeSlider),[i,a]=n.useState(null),s=(0,o.s)(t,e=>a(e)),l=n.useMemo(()=>i?r().findIndex(e=>e.ref.current===i):-1,[r,i]);return(0,p.jsx)(G,{...e,ref:s,index:l})}),G=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:i,name:s,...l}=e,d=k(L,r),h=M(L,r),[f,g]=n.useState(null),m=(0,o.s)(t,e=>g(e)),v=!f||d.form||!!f.closest("form"),y=(0,c.X)(f),x=d.values[i],w=void 0===x?0:U(x,d.min,d.max),_=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(i,d.values.length),S=null==y?void 0:y[h.size],C=S?function(e,t,r){let n=e/2,i=B([0,50],[0,n]);return(n-i(t)*r)*r}(S,w,h.direction):0;return n.useEffect(()=>{if(f)return d.thumbs.add(f),()=>{d.thumbs.delete(f)}},[f,d.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[h.startEdge]:"calc(".concat(w,"% + ").concat(C,"px)")},children:[(0,p.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(u.sG.span,{role:"slider","aria-label":e["aria-label"]||_,"aria-valuemin":d.min,"aria-valuenow":x,"aria-valuemax":d.max,"aria-orientation":d.orientation,"data-orientation":d.orientation,"data-disabled":d.disabled?"":void 0,tabIndex:d.disabled?void 0:0,...l,ref:m,style:void 0===x?{display:"none"}:e.style,onFocus:(0,a.m)(e.onFocus,()=>{d.valueIndexToChangeRef.current=i})})}),v&&(0,p.jsx)(j,{name:null!=s?s:d.name?d.name+(d.values.length>1?"[]":""):void 0,form:d.form,value:x},i)]})});I.displayName=L;var j=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:i,...a}=e,s=n.useRef(null),l=(0,o.s)(s,t),d=(0,h.Z)(i);return n.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(d!==i&&t){let r=new Event("input",{bubbles:!0});t.call(e,i),e.dispatchEvent(r)}},[d,i]),(0,p.jsx)(u.sG.input,{style:{display:"none"},...a,ref:l,defaultValue:i})});function U(e,t,r){return(0,i.q)(100/(r-t)*(e-t),[0,100])}function B(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}j.displayName="RadioBubbleInput";var z=P,H=N,V=O,W=I},4185:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Image=void 0;let n=r(6509),i=r(8949),a=r(3306),o=r(6878),s=r(6394);class l extends a.Shape{constructor(e){super(e),this._loadListener=()=>{this._requestDraw()},this.on("imageChange.konva",e=>{this._removeImageLoad(e.oldVal),this._setImageLoad()}),this._setImageLoad()}_setImageLoad(){let e=this.image();e&&e.complete||(!e||4!==e.readyState)&&e&&e.addEventListener&&e.addEventListener("load",this._loadListener)}_removeImageLoad(e){e&&e.removeEventListener&&e.removeEventListener("load",this._loadListener)}destroy(){return this._removeImageLoad(this.image()),super.destroy(),this}_useBufferCanvas(){let e=!!this.cornerRadius(),t=this.hasShadow();return!!e&&!!t||super._useBufferCanvas(!0)}_sceneFunc(e){let t,r=this.getWidth(),i=this.getHeight(),a=this.cornerRadius(),o=this.attrs.image;if(o){let e=this.attrs.cropWidth,n=this.attrs.cropHeight;t=e&&n?[o,this.cropX(),this.cropY(),e,n,0,0,r,i]:[o,0,0,r,i]}(this.hasFill()||this.hasStroke()||a)&&(e.beginPath(),a?n.Util.drawRoundedRectPath(e,r,i,a):e.rect(0,0,r,i),e.closePath(),e.fillStrokeShape(this)),o&&(a&&e.clip(),e.drawImage.apply(e,t))}_hitFunc(e){let t=this.width(),r=this.height(),i=this.cornerRadius();e.beginPath(),i?n.Util.drawRoundedRectPath(e,t,r,i):e.rect(0,0,t,r),e.closePath(),e.fillStrokeShape(this)}getWidth(){var e,t;return null!=(e=this.attrs.width)?e:null==(t=this.image())?void 0:t.width}getHeight(){var e,t;return null!=(e=this.attrs.height)?e:null==(t=this.image())?void 0:t.height}static fromURL(e,t,r=null){let i=n.Util.createImageElement();i.onload=function(){t(new l({image:i}))},i.onerror=r,i.crossOrigin="Anonymous",i.src=e}}t.Image=l,l.prototype.className="Image",(0,o._registerNode)(l),i.Factory.addGetterSetter(l,"cornerRadius",0,(0,s.getNumberOrArrayOfNumbersValidator)(4)),i.Factory.addGetterSetter(l,"image"),i.Factory.addComponentsGetterSetter(l,"crop",["x","y","width","height"]),i.Factory.addGetterSetter(l,"cropX",0,(0,s.getNumberValidator)()),i.Factory.addGetterSetter(l,"cropY",0,(0,s.getNumberValidator)()),i.Factory.addGetterSetter(l,"cropWidth",0,(0,s.getNumberValidator)()),i.Factory.addGetterSetter(l,"cropHeight",0,(0,s.getNumberValidator)())},4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4315:(e,t,r)=>{r.d(t,{jH:()=>a});var n=r(2115);r(5155);var i=n.createContext(void 0);function a(e){let t=n.useContext(i);return e||t||"ltr"}},4357:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4378:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115),i=r(7650),a=r(3655),o=r(2712),s=r(5155),l=n.forwardRef((e,t)=>{var r,l;let{container:d,...h}=e,[c,u]=n.useState(!1);(0,o.N)(()=>u(!0),[]);let f=d||c&&(null==(l=globalThis)||null==(r=l.document)?void 0:r.body);return f?i.createPortal((0,s.jsx)(a.sG.div,{...h,ref:t}),f):null});l.displayName="Portal"},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4421:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),t.HitCanvas=t.SceneCanvas=t.Canvas=void 0;let i=r(6509),a=r(9012),o=r(6878);class s{constructor(e){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;let t=(e||{}).pixelRatio||o.Konva.pixelRatio||function(){if(n)return n;let e=i.Util.createCanvasElement(),t=e.getContext("2d");return n=(o.Konva._global.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),i.Util.releaseCanvas(e),n}();this.pixelRatio=t,this._canvas=i.Util.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(e){let t=this.pixelRatio;this.pixelRatio=e,this.setSize(this.getWidth()/t,this.getHeight()/t)}setWidth(e){this.width=this._canvas.width=e*this.pixelRatio,this._canvas.style.width=e+"px";let t=this.pixelRatio;this.getContext()._context.scale(t,t)}setHeight(e){this.height=this._canvas.height=e*this.pixelRatio,this._canvas.style.height=e+"px";let t=this.pixelRatio;this.getContext()._context.scale(t,t)}getWidth(){return this.width}getHeight(){return this.height}setSize(e,t){this.setWidth(e||0),this.setHeight(t||0)}toDataURL(e,t){try{return this._canvas.toDataURL(e,t)}catch(e){try{return this._canvas.toDataURL()}catch(e){return i.Util.error("Unable to get data URL. "+e.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}}t.Canvas=s;class l extends s{constructor(e={width:0,height:0,willReadFrequently:!1}){super(e),this.context=new a.SceneContext(this,{willReadFrequently:e.willReadFrequently}),this.setSize(e.width,e.height)}}t.SceneCanvas=l;class d extends s{constructor(e={width:0,height:0}){super(e),this.hitCanvas=!0,this.context=new a.HitContext(this),this.setSize(e.width,e.height)}}t.HitCanvas=d},4555:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getCapturedShape=function(e){return i.get(e)},t.createEvent=o,t.hasPointerCapture=function(e,t){return i.get(e)===t},t.setPointerCapture=function(e,t){s(e),t.getStage()&&(i.set(e,t),a&&t._fire("gotpointercapture",o(new PointerEvent("gotpointercapture"))))},t.releaseCapture=s;let n=r(6878),i=new Map,a=void 0!==n.Konva._global.PointerEvent;function o(e){return{evt:e,pointerId:e.pointerId}}function s(e,t){let r=i.get(e);if(!r)return;let n=r.getStage();n&&n.content,i.delete(e),a&&r._fire("lostpointercapture",o(new PointerEvent("lostpointercapture")))}},4835:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5003:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),e.exports=r(2967).Konva},5010:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Ellipse=void 0;let n=r(8949),i=r(3306),a=r(6394),o=r(6878);class s extends i.Shape{_sceneFunc(e){let t=this.radiusX(),r=this.radiusY();e.beginPath(),e.save(),t!==r&&e.scale(1,r/t),e.arc(0,0,t,0,2*Math.PI,!1),e.restore(),e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.radiusX()}getHeight(){return 2*this.radiusY()}setWidth(e){this.radiusX(e/2)}setHeight(e){this.radiusY(e/2)}}t.Ellipse=s,s.prototype.className="Ellipse",s.prototype._centroid=!0,s.prototype._attrsAffectingSize=["radiusX","radiusY"],(0,o._registerNode)(s),n.Factory.addComponentsGetterSetter(s,"radius",["x","y"]),n.Factory.addGetterSetter(s,"radiusX",0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(s,"radiusY",0,(0,a.getNumberValidator)())},5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5152:(e,t,r)=>{r.d(t,{Mz:()=>e1,i3:()=>e5,UC:()=>e2,bL:()=>e0,Bk:()=>eG});var n=r(2115);let i=["top","right","bottom","left"],a=Math.min,o=Math.max,s=Math.round,l=Math.floor,d=e=>({x:e,y:e}),h={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function u(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function g(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let v=new Set(["top","bottom"]);function y(e){return v.has(f(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>c[e])}let x=["left","right"],w=["right","left"],_=["top","bottom"],S=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>h[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function A(e,t,r){let n,{reference:i,floating:a}=e,o=y(t),s=g(y(t)),l=m(s),d=f(t),h="y"===o,c=i.x+i.width/2-a.width/2,u=i.y+i.height/2-a.height/2,v=i[l]/2-a[l]/2;switch(d){case"top":n={x:c,y:i.y-a.height};break;case"bottom":n={x:c,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:u};break;case"left":n={x:i.x-a.width,y:u};break;default:n={x:i.x,y:i.y}}switch(p(t)){case"start":n[s]-=v*(r&&h?-1:1);break;case"end":n[s]+=v*(r&&h?-1:1)}return n}let M=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:a=[],platform:o}=r,s=a.filter(Boolean),l=await (null==o.isRTL?void 0:o.isRTL(t)),d=await o.getElementRects({reference:e,floating:t,strategy:i}),{x:h,y:c}=A(d,n,l),u=n,f={},p=0;for(let r=0;r<s.length;r++){let{name:a,fn:g}=s[r],{x:m,y:v,data:y,reset:b}=await g({x:h,y:c,initialPlacement:n,placement:u,strategy:i,middlewareData:f,rects:d,platform:o,elements:{reference:e,floating:t}});h=null!=m?m:h,c=null!=v?v:c,f={...f,[a]:{...f[a],...y}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(u=b.placement),b.rects&&(d=!0===b.rects?await o.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:h,y:c}=A(d,u,l)),r=-1)}return{x:h,y:c,placement:u,strategy:i,middlewareData:f}};async function T(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:a,rects:o,elements:s,strategy:l}=e,{boundary:d="clippingAncestors",rootBoundary:h="viewport",elementContext:c="floating",altBoundary:f=!1,padding:p=0}=u(t,e),g=k(p),m=s[f?"floating"===c?"reference":"floating":c],v=P(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(m)))||r?m:m.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:d,rootBoundary:h,strategy:l})),y="floating"===c?{x:n,y:i,width:o.floating.width,height:o.floating.height}:o.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),x=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},w=P(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:b,strategy:l}):y);return{top:(v.top-w.top+g.top)/x.y,bottom:(w.bottom-v.bottom+g.bottom)/x.y,left:(v.left-w.left+g.left)/x.x,right:(w.right-v.right+g.right)/x.x}}function E(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return i.some(t=>e[t]>=0)}let D=new Set(["left","top"]);async function N(e,t){let{placement:r,platform:n,elements:i}=e,a=await (null==n.isRTL?void 0:n.isRTL(i.floating)),o=f(r),s=p(r),l="y"===y(r),d=D.has(o)?-1:1,h=a&&l?-1:1,c=u(t,e),{mainAxis:g,crossAxis:m,alignmentAxis:v}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return s&&"number"==typeof v&&(m="end"===s?-1*v:v),l?{x:m*h,y:g*d}:{x:g*d,y:m*h}}function F(){return"undefined"!=typeof window}function O(e){return G(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(G(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function G(e){return!!F()&&(e instanceof Node||e instanceof L(e).Node)}function j(e){return!!F()&&(e instanceof Element||e instanceof L(e).Element)}function U(e){return!!F()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function B(e){return!!F()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}let z=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!z.has(i)}let V=new Set(["table","td","th"]),W=[":popover-open",":modal"];function K(e){return W.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let Y=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function $(e){let t=Z(),r=j(e)?ee(e):e;return Y.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||q.some(e=>(r.willChange||"").includes(e))||X.some(e=>(r.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(O(e))}function ee(e){return L(e).getComputedStyle(e)}function et(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||I(e);return B(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=er(t);return J(r)?t.ownerDocument?t.ownerDocument.body:t.body:U(r)&&H(r)?r:e(r)}(e),a=i===(null==(n=e.ownerDocument)?void 0:n.body),o=L(i);if(a){let e=ei(o);return t.concat(o,o.visualViewport||[],H(i)?i:[],e&&r?en(e):[])}return t.concat(i,en(i,[],r))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=U(e),a=i?e.offsetWidth:r,o=i?e.offsetHeight:n,l=s(r)!==a||s(n)!==o;return l&&(r=a,n=o),{width:r,height:n,$:l}}function eo(e){return j(e)?e:e.contextElement}function es(e){let t=eo(e);if(!U(t))return d(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:a}=ea(t),o=(a?s(r.width):r.width)/n,l=(a?s(r.height):r.height)/i;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}let el=d(0);function ed(e){let t=L(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function eh(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),o=eo(e),s=d(1);t&&(n?j(n)&&(s=es(n)):s=es(e));let l=(void 0===(i=r)&&(i=!1),n&&(!i||n===L(o))&&i)?ed(o):d(0),h=(a.left+l.x)/s.x,c=(a.top+l.y)/s.y,u=a.width/s.x,f=a.height/s.y;if(o){let e=L(o),t=n&&j(n)?L(n):n,r=e,i=ei(r);for(;i&&n&&t!==r;){let e=es(i),t=i.getBoundingClientRect(),n=ee(i),a=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,o=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;h*=e.x,c*=e.y,u*=e.x,f*=e.y,h+=a,c+=o,i=ei(r=L(i))}}return P({width:u,height:f,x:h,y:c})}function ec(e,t){let r=et(e).scrollLeft;return t?t.left+r:eh(I(e)).left+r}function eu(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ec(e,n)),y:n.top+t.scrollTop}}let ef=new Set(["absolute","fixed"]);function ep(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=L(e),n=I(e),i=r.visualViewport,a=n.clientWidth,o=n.clientHeight,s=0,l=0;if(i){a=i.width,o=i.height;let e=Z();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:a,height:o,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=I(e),r=et(e),n=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=o(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+ec(e),l=-r.scrollTop;return"rtl"===ee(n).direction&&(s+=o(t.clientWidth,n.clientWidth)-i),{width:i,height:a,x:s,y:l}}(I(e));else if(j(t))n=function(e,t){let r=eh(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,a=U(e)?es(e):d(1),o=e.clientWidth*a.x,s=e.clientHeight*a.y;return{width:o,height:s,x:i*a.x,y:n*a.y}}(t,r);else{let r=ed(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return P(n)}function eg(e){return"static"===ee(e).position}function em(e,t){if(!U(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return I(e)===r&&(r=r.ownerDocument.body),r}function ev(e,t){var r;let n=L(e);if(K(e))return n;if(!U(e)){let t=er(e);for(;t&&!J(t);){if(j(t)&&!eg(t))return t;t=er(t)}return n}let i=em(e,t);for(;i&&(r=i,V.has(O(r)))&&eg(i);)i=em(i,t);return i&&J(i)&&eg(i)&&!$(i)?n:i||function(e){let t=er(e);for(;U(t)&&!J(t);){if($(t))return t;if(K(t))break;t=er(t)}return null}(e)||n}let ey=async function(e){let t=this.getOffsetParent||ev,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=U(t),i=I(t),a="fixed"===r,o=eh(e,!0,a,t),s={scrollLeft:0,scrollTop:0},l=d(0);if(n||!n&&!a)if(("body"!==O(t)||H(i))&&(s=et(t)),n){let e=eh(t,!0,a,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=ec(i));a&&!n&&i&&(l.x=ec(i));let h=!i||n||a?d(0):eu(i,s);return{x:o.left+s.scrollLeft-l.x-h.x,y:o.top+s.scrollTop-l.y-h.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,a="fixed"===i,o=I(n),s=!!t&&K(t.floating);if(n===o||s&&a)return r;let l={scrollLeft:0,scrollTop:0},h=d(1),c=d(0),u=U(n);if((u||!u&&!a)&&(("body"!==O(n)||H(o))&&(l=et(n)),U(n))){let e=eh(n);h=es(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let f=!o||u||a?d(0):eu(o,l,!0);return{width:r.width*h.x,height:r.height*h.y,x:r.x*h.x-l.scrollLeft*h.x+c.x+f.x,y:r.y*h.y-l.scrollTop*h.y+c.y+f.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,s=[..."clippingAncestors"===r?K(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>j(e)&&"body"!==O(e)),i=null,a="fixed"===ee(e).position,o=a?er(e):e;for(;j(o)&&!J(o);){let t=ee(o),r=$(o);r||"fixed"!==t.position||(i=null),(a?!r&&!i:!r&&"static"===t.position&&!!i&&ef.has(i.position)||H(o)&&!r&&function e(t,r){let n=er(t);return!(n===r||!j(n)||J(n))&&("fixed"===ee(n).position||e(n,r))}(e,o))?n=n.filter(e=>e!==o):i=t,o=er(o)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=s[0],d=s.reduce((e,r)=>{let n=ep(t,r,i);return e.top=o(n.top,e.top),e.right=a(n.right,e.right),e.bottom=a(n.bottom,e.bottom),e.left=o(n.left,e.left),e},ep(t,l,i));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}},getOffsetParent:ev,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ea(e);return{width:t,height:r}},getScale:es,isElement:j,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:s,platform:l,elements:d,middlewareData:h}=t,{element:c,padding:f=0}=u(e,t)||{};if(null==c)return{};let v=k(f),b={x:r,y:n},x=g(y(i)),w=m(x),_=await l.getDimensions(c),S="y"===x,C=S?"clientHeight":"clientWidth",P=s.reference[w]+s.reference[x]-b[x]-s.floating[w],A=b[x]-s.reference[x],M=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),T=M?M[C]:0;T&&await (null==l.isElement?void 0:l.isElement(M))||(T=d.floating[C]||s.floating[w]);let E=T/2-_[w]/2-1,R=a(v[S?"top":"left"],E),D=a(v[S?"bottom":"right"],E),N=T-_[w]-D,F=T/2-_[w]/2+(P/2-A/2),O=o(R,a(F,N)),L=!h.arrow&&null!=p(i)&&F!==O&&s.reference[w]/2-(F<R?R:D)-_[w]/2<0,I=L?F<R?F-R:F-N:0;return{[x]:b[x]+I,data:{[x]:O,centerOffset:F-O-I,...L&&{alignmentOffset:I}},reset:L}}});var e_=r(7650),eS="undefined"!=typeof document?n.useLayoutEffect:function(){};function eC(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eC(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!eC(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ek(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eP(e,t){let r=ek(e);return Math.round(t*r)/r}function eA(e){let t=n.useRef(e);return eS(()=>{t.current=e}),t}var eM=r(3655),eT=r(5155),eE=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...a}=e;return(0,eT.jsx)(eM.sG.svg,{...a,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eT.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eR=r(6101),eD=r(6081),eN=r(9033),eF=r(2712),eO=r(1275),eL="Popper",[eI,eG]=(0,eD.A)(eL),[ej,eU]=eI(eL),eB=e=>{let{__scopePopper:t,children:r}=e,[i,a]=n.useState(null);return(0,eT.jsx)(ej,{scope:t,anchor:i,onAnchorChange:a,children:r})};eB.displayName=eL;var ez="PopperAnchor",eH=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...a}=e,o=eU(ez,r),s=n.useRef(null),l=(0,eR.s)(t,s);return n.useEffect(()=>{o.onAnchorChange((null==i?void 0:i.current)||s.current)}),i?null:(0,eT.jsx)(eM.sG.div,{...a,ref:l})});eH.displayName=ez;var eV="PopperContent",[eW,eK]=eI(eV),eY=n.forwardRef((e,t)=>{var r,i,s,d,h,c,v,k;let{__scopePopper:P,side:A="bottom",sideOffset:F=0,align:O="center",alignOffset:L=0,arrowPadding:G=0,avoidCollisions:j=!0,collisionBoundary:U=[],collisionPadding:B=0,sticky:z="partial",hideWhenDetached:H=!1,updatePositionStrategy:V="optimized",onPlaced:W,...K}=e,Y=eU(eV,P),[q,X]=n.useState(null),$=(0,eR.s)(t,e=>X(e)),[Z,Q]=n.useState(null),J=(0,eO.X)(Z),ee=null!=(v=null==J?void 0:J.width)?v:0,et=null!=(k=null==J?void 0:J.height)?k:0,er="number"==typeof B?B:{top:0,right:0,bottom:0,left:0,...B},ei=Array.isArray(U)?U:[U],ea=ei.length>0,es={padding:er,boundary:ei.filter(eZ),altBoundary:ea},{refs:el,floatingStyles:ed,placement:ec,isPositioned:eu,middlewareData:ef}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:a,elements:{reference:o,floating:s}={},transform:l=!0,whileElementsMounted:d,open:h}=e,[c,u]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=n.useState(i);eC(f,i)||p(i);let[g,m]=n.useState(null),[v,y]=n.useState(null),b=n.useCallback(e=>{e!==S.current&&(S.current=e,m(e))},[]),x=n.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),w=o||g,_=s||v,S=n.useRef(null),C=n.useRef(null),k=n.useRef(c),P=null!=d,A=eA(d),T=eA(a),E=eA(h),R=n.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:r,middleware:f};T.current&&(e.platform=T.current),((e,t,r)=>{let n=new Map,i={platform:eb,...r},a={...i.platform,_c:n};return M(e,t,{...i,platform:a})})(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==E.current};D.current&&!eC(k.current,t)&&(k.current=t,e_.flushSync(()=>{u(t)}))})},[f,t,r,T,E]);eS(()=>{!1===h&&k.current.isPositioned&&(k.current.isPositioned=!1,u(e=>({...e,isPositioned:!1})))},[h]);let D=n.useRef(!1);eS(()=>(D.current=!0,()=>{D.current=!1}),[]),eS(()=>{if(w&&(S.current=w),_&&(C.current=_),w&&_){if(A.current)return A.current(w,_,R);R()}},[w,_,R,A,P]);let N=n.useMemo(()=>({reference:S,floating:C,setReference:b,setFloating:x}),[b,x]),F=n.useMemo(()=>({reference:w,floating:_}),[w,_]),O=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!F.floating)return e;let t=eP(F.floating,c.x),n=eP(F.floating,c.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...ek(F.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,F.floating,c.x,c.y]);return n.useMemo(()=>({...c,update:R,refs:N,elements:F,floatingStyles:O}),[c,R,N,F,O])}({strategy:"fixed",placement:A+("center"!==O?"-"+O:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:s=!0,ancestorResize:d=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,f=eo(e),p=s||d?[...f?en(f):[],...en(t)]:[];p.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),d&&e.addEventListener("resize",r)});let g=f&&c?function(e,t){let r,n=null,i=I(e);function s(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function d(h,c){void 0===h&&(h=!1),void 0===c&&(c=1),s();let u=e.getBoundingClientRect(),{left:f,top:p,width:g,height:m}=u;if(h||t(),!g||!m)return;let v=l(p),y=l(i.clientWidth-(f+g)),b={rootMargin:-v+"px "+-y+"px "+-l(i.clientHeight-(p+m))+"px "+-l(f)+"px",threshold:o(0,a(1,c))||1},x=!0;function w(t){let n=t[0].intersectionRatio;if(n!==c){if(!x)return d();n?d(!1,n):r=setTimeout(()=>{d(!1,1e-7)},1e3)}1!==n||ex(u,e.getBoundingClientRect())||d(),x=!1}try{n=new IntersectionObserver(w,{...b,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(w,b)}n.observe(e)}(!0),s}(f,r):null,m=-1,v=null;h&&(v=new ResizeObserver(e=>{let[n]=e;n&&n.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),r()}),f&&!u&&v.observe(f),v.observe(t));let y=u?eh(e):null;return u&&function t(){let n=eh(e);y&&!ex(y,n)&&r(),y=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;p.forEach(e=>{s&&e.removeEventListener("scroll",r),d&&e.removeEventListener("resize",r)}),null==g||g(),null==(e=v)||e.disconnect(),v=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===V})},elements:{reference:Y.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:a,placement:o,middlewareData:s}=t,l=await N(t,e);return o===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:i+l.x,y:a+l.y,data:{...l,placement:o}}}}}(e),options:[e,t]}))({mainAxis:F+et,alignmentAxis:L}),j&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:s=!0,crossAxis:l=!1,limiter:d={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...h}=u(e,t),c={x:r,y:n},p=await T(t,h),m=y(f(i)),v=g(m),b=c[v],x=c[m];if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=b+p[e],n=b-p[t];b=o(r,a(b,n))}if(l){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",r=x+p[e],n=x-p[t];x=o(r,a(x,n))}let w=d.fn({...t,[v]:b,[m]:x});return{...w,data:{x:w.x-r,y:w.y-n,enabled:{[v]:s,[m]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===z?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:a,middlewareData:o}=t,{offset:s=0,mainAxis:l=!0,crossAxis:d=!0}=u(e,t),h={x:r,y:n},c=y(i),p=g(c),m=h[p],v=h[c],b=u(s,t),x="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===p?"height":"width",t=a.reference[p]-a.floating[e]+x.mainAxis,r=a.reference[p]+a.reference[e]-x.mainAxis;m<t?m=t:m>r&&(m=r)}if(d){var w,_;let e="y"===p?"width":"height",t=D.has(f(i)),r=a.reference[c]-a.floating[e]+(t&&(null==(w=o.offset)?void 0:w[c])||0)+(t?0:x.crossAxis),n=a.reference[c]+a.reference[e]+(t?0:(null==(_=o.offset)?void 0:_[c])||0)-(t?x.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[p]:m,[c]:v}}}}(e),options:[e,t]}))():void 0,...es}),j&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,a,o;let{placement:s,middlewareData:l,rects:d,initialPlacement:h,platform:c,elements:v}=t,{mainAxis:k=!0,crossAxis:P=!0,fallbackPlacements:A,fallbackStrategy:M="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:R=!0,...D}=u(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let N=f(s),F=y(h),O=f(h)===h,L=await (null==c.isRTL?void 0:c.isRTL(v.floating)),I=A||(O||!R?[C(h)]:function(e){let t=C(e);return[b(e),t,b(t)]}(h)),G="none"!==E;!A&&G&&I.push(...function(e,t,r,n){let i=p(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?w:x;return t?x:w;case"left":case"right":return t?_:S;default:return[]}}(f(e),"start"===r,n);return i&&(a=a.map(e=>e+"-"+i),t&&(a=a.concat(a.map(b)))),a}(h,R,E,L));let j=[h,...I],U=await T(t,D),B=[],z=(null==(n=l.flip)?void 0:n.overflows)||[];if(k&&B.push(U[N]),P){let e=function(e,t,r){void 0===r&&(r=!1);let n=p(e),i=g(y(e)),a=m(i),o="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(o=C(o)),[o,C(o)]}(s,d,L);B.push(U[e[0]],U[e[1]])}if(z=[...z,{placement:s,overflows:B}],!B.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=j[e];if(t&&("alignment"!==P||F===y(t)||z.every(e=>e.overflows[0]>0&&y(e.placement)===F)))return{data:{index:e,overflows:z},reset:{placement:t}};let r=null==(a=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(M){case"bestFit":{let e=null==(o=z.filter(e=>{if(G){let t=y(e.placement);return t===F||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(r=e);break}case"initialPlacement":r=h}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}))({...es}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,s,{placement:l,rects:d,platform:h,elements:c}=t,{apply:g=()=>{},...m}=u(e,t),v=await T(t,m),b=f(l),x=p(l),w="y"===y(l),{width:_,height:S}=d.floating;"top"===b||"bottom"===b?(i=b,s=x===(await (null==h.isRTL?void 0:h.isRTL(c.floating))?"start":"end")?"left":"right"):(s=b,i="end"===x?"top":"bottom");let C=S-v.top-v.bottom,k=_-v.left-v.right,P=a(S-v[i],C),A=a(_-v[s],k),M=!t.middlewareData.shift,E=P,R=A;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(R=k),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(E=C),M&&!x){let e=o(v.left,0),t=o(v.right,0),r=o(v.top,0),n=o(v.bottom,0);w?R=_-2*(0!==e||0!==t?e+t:o(v.left,v.right)):E=S-2*(0!==r||0!==n?r+n:o(v.top,v.bottom))}await g({...t,availableWidth:R,availableHeight:E});let D=await h.getDimensions(c.floating);return _!==D.width||S!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...es,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:i}=e,{width:a,height:o}=r.reference,s=t.floating.style;s.setProperty("--radix-popper-available-width","".concat(n,"px")),s.setProperty("--radix-popper-available-height","".concat(i,"px")),s.setProperty("--radix-popper-anchor-width","".concat(a,"px")),s.setProperty("--radix-popper-anchor-height","".concat(o,"px"))}}),Z&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ew({element:r.current,padding:n}).fn(t):{}:r?ew({element:r,padding:n}).fn(t):{}}}))(e),options:[e,t]}))({element:Z,padding:G}),eQ({arrowWidth:ee,arrowHeight:et}),H&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=u(e,t);switch(n){case"referenceHidden":{let e=E(await T(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=E(await T(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...es})]}),[ep,eg]=eJ(ec),em=(0,eN.c)(W);(0,eF.N)(()=>{eu&&(null==em||em())},[eu,em]);let ev=null==(r=ef.arrow)?void 0:r.x,ey=null==(i=ef.arrow)?void 0:i.y,eE=(null==(s=ef.arrow)?void 0:s.centerOffset)!==0,[eD,eL]=n.useState();return(0,eF.N)(()=>{q&&eL(window.getComputedStyle(q).zIndex)},[q]),(0,eT.jsx)("div",{ref:el.setFloating,"data-radix-popper-content-wrapper":"",style:{...ed,transform:eu?ed.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eD,"--radix-popper-transform-origin":[null==(d=ef.transformOrigin)?void 0:d.x,null==(h=ef.transformOrigin)?void 0:h.y].join(" "),...(null==(c=ef.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eT.jsx)(eW,{scope:P,placedSide:ep,onArrowChange:Q,arrowX:ev,arrowY:ey,shouldHideArrow:eE,children:(0,eT.jsx)(eM.sG.div,{"data-side":ep,"data-align":eg,...K,ref:$,style:{...K.style,animation:eu?void 0:"none"}})})})});eY.displayName=eV;var eq="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},e$=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=eK(eq,r),a=eX[i.placedSide];return(0,eT.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eT.jsx)(eE,{...n,ref:t,style:{...n.style,display:"block"}})})});function eZ(e){return null!==e}e$.displayName=eq;var eQ=e=>({name:"transformOrigin",options:e,fn(t){var r,n,i,a,o;let{placement:s,rects:l,middlewareData:d}=t,h=(null==(r=d.arrow)?void 0:r.centerOffset)!==0,c=h?0:e.arrowWidth,u=h?0:e.arrowHeight,[f,p]=eJ(s),g={start:"0%",center:"50%",end:"100%"}[p],m=(null!=(a=null==(n=d.arrow)?void 0:n.x)?a:0)+c/2,v=(null!=(o=null==(i=d.arrow)?void 0:i.y)?o:0)+u/2,y="",b="";return"bottom"===f?(y=h?g:"".concat(m,"px"),b="".concat(-u,"px")):"top"===f?(y=h?g:"".concat(m,"px"),b="".concat(l.floating.height+u,"px")):"right"===f?(y="".concat(-u,"px"),b=h?g:"".concat(v,"px")):"left"===f&&(y="".concat(l.floating.width+u,"px"),b=h?g:"".concat(v,"px")),{data:{x:y,y:b}}}});function eJ(e){let[t,r="center"]=e.split("-");return[t,r]}var e0=eB,e1=eH,e2=eY,e5=e$},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5220:(e,t,r)=>{e.exports=r(1724)},5344:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Circle=void 0;let n=r(8949),i=r(3306),a=r(6394),o=r(6878);class s extends i.Shape{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.attrs.radius||0,0,2*Math.PI,!1),e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(e){this.radius()!==e/2&&this.radius(e/2)}setHeight(e){this.radius()!==e/2&&this.radius(e/2)}}t.Circle=s,s.prototype._centroid=!0,s.prototype.className="Circle",s.prototype._attrsAffectingSize=["radius"],(0,o._registerNode)(s),n.Factory.addGetterSetter(s,"radius",0,(0,a.getNumberValidator)())},5452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>ei,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>J});var n=r(2115),i=r(5185),a=r(6101),o=r(6081),s=r(1285),l=r(5845),d=r(9178),h=r(7900),c=r(4378),u=r(8905),f=r(3655),p=r(2293),g=r(3795),m=r(8168),v=r(9708),y=r(5155),b="Dialog",[x,w]=(0,o.A)(b),[_,S]=x(b),C=e=>{let{__scopeDialog:t,children:r,open:i,defaultOpen:a,onOpenChange:o,modal:d=!0}=e,h=n.useRef(null),c=n.useRef(null),[u,f]=(0,l.i)({prop:i,defaultProp:null!=a&&a,onChange:o,caller:b});return(0,y.jsx)(_,{scope:t,triggerRef:h,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:u,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};C.displayName=b;var k="DialogTrigger",P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(k,r),s=(0,a.s)(t,o.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":K(o.open),...n,ref:s,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});P.displayName=k;var A="DialogPortal",[M,T]=x(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:i,container:a}=e,o=S(A,t);return(0,y.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(i,e=>(0,y.jsx)(u.C,{present:r||o.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=A;var R="DialogOverlay",D=n.forwardRef((e,t)=>{let r=T(R,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=S(R,e.__scopeDialog);return a.modal?(0,y.jsx)(u.C,{present:n||a.open,children:(0,y.jsx)(F,{...i,ref:t})}):null});D.displayName=R;var N=(0,v.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=S(R,r);return(0,y.jsx)(g.A,{as:N,allowPinchZoom:!0,shards:[i.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":K(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",L=n.forwardRef((e,t)=>{let r=T(O,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=S(O,e.__scopeDialog);return(0,y.jsx)(u.C,{present:n||a.open,children:a.modal?(0,y.jsx)(I,{...i,ref:t}):(0,y.jsx)(G,{...i,ref:t})})});L.displayName=O;var I=n.forwardRef((e,t)=>{let r=S(O,e.__scopeDialog),o=n.useRef(null),s=(0,a.s)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(j,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let r=S(O,e.__scopeDialog),i=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(j,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(i.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:t=>{var n,o;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),j=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:i,onOpenAutoFocus:o,onCloseAutoFocus:s,...l}=e,c=S(O,r),u=n.useRef(null),f=(0,a.s)(t,u);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(h.n,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:o,onUnmountAutoFocus:s,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":K(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($,{titleId:c.titleId}),(0,y.jsx)(Z,{contentRef:u,descriptionId:c.descriptionId})]})]})}),U="DialogTitle",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=S(U,r);return(0,y.jsx)(f.sG.h2,{id:i.titleId,...n,ref:t})});B.displayName=U;var z="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=S(z,r);return(0,y.jsx)(f.sG.p,{id:i.descriptionId,...n,ref:t})});H.displayName=z;var V="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=S(V,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,i.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}W.displayName=V;var Y="DialogTitleWarning",[q,X]=(0,o.q)(Y,{contentName:O,titleName:U,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=X(Y),i="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(i))},[i,t]),null},Z=e=>{let{contentRef:t,descriptionId:r}=e,i=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Q=C,J=P,ee=E,et=D,er=L,en=B,ei=H,ea=W},5503:(e,t,r)=>{r.d(t,{Z:()=>i});var n=r(2115);function i(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5552:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("flip-vertical",[["path",{d:"M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3",key:"14bfxa"}],["path",{d:"M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3",key:"14rx03"}],["path",{d:"M4 12H2",key:"rhcxmi"}],["path",{d:"M10 12H8",key:"s88cx1"}],["path",{d:"M16 12h-2",key:"10asgb"}],["path",{d:"M22 12h-2",key:"14jgyd"}]])},5673:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Arrow=void 0;let n=r(8949),i=r(8650),a=r(6394),o=r(6878),s=r(3927);class l extends i.Line{_sceneFunc(e){let t,r;super._sceneFunc(e);let n=2*Math.PI,i=this.points(),a=i,o=0!==this.tension()&&i.length>4;o&&(a=this.getTensionPoints());let l=this.pointerLength(),d=i.length;if(o){let e=[a[a.length-4],a[a.length-3],a[a.length-2],a[a.length-1],i[d-2],i[d-1]],n=s.Path.calcLength(a[a.length-4],a[a.length-3],"C",e),o=s.Path.getPointOnQuadraticBezier(Math.min(1,1-l/n),e[0],e[1],e[2],e[3],e[4],e[5]);t=i[d-2]-o.x,r=i[d-1]-o.y}else t=i[d-2]-i[d-4],r=i[d-1]-i[d-3];let h=(Math.atan2(r,t)+n)%n,c=this.pointerWidth();this.pointerAtEnding()&&(e.save(),e.beginPath(),e.translate(i[d-2],i[d-1]),e.rotate(h),e.moveTo(0,0),e.lineTo(-l,c/2),e.lineTo(-l,-c/2),e.closePath(),e.restore(),this.__fillStroke(e)),this.pointerAtBeginning()&&(e.save(),e.beginPath(),e.translate(i[0],i[1]),o?(t=(a[0]+a[2])/2-i[0],r=(a[1]+a[3])/2-i[1]):(t=i[2]-i[0],r=i[3]-i[1]),e.rotate((Math.atan2(-r,-t)+n)%n),e.moveTo(0,0),e.lineTo(-l,c/2),e.lineTo(-l,-c/2),e.closePath(),e.restore(),this.__fillStroke(e))}__fillStroke(e){let t=this.dashEnabled();t&&(this.attrs.dashEnabled=!1,e.setLineDash([])),e.fillStrokeShape(this),t&&(this.attrs.dashEnabled=!0)}getSelfRect(){let e=super.getSelfRect(),t=this.pointerWidth()/2;return{x:e.x,y:e.y-t,width:e.width,height:e.height+2*t}}}t.Arrow=l,l.prototype.className="Arrow",(0,o._registerNode)(l),n.Factory.addGetterSetter(l,"pointerLength",10,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(l,"pointerWidth",10,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(l,"pointerAtBeginning",!1),n.Factory.addGetterSetter(l,"pointerAtEnding",!0)},5845:(e,t,r)=>{r.d(t,{i:()=>s});var n,i=r(2115),a=r(2712),o=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||a.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),a=i.useRef(r),s=i.useRef(t);return o(()=>{s.current=t},[t]),i.useEffect(()=>{a.current!==r&&(s.current?.(r),a.current=r)},[r,a]),[r,n,s]}({defaultProp:t,onChange:r}),d=void 0!==e,h=d?e:a;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==d){let t=d?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=d},[d,n])}return[h,i.useCallback(t=>{if(d){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[d,e,s,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>a});var n=r(2115),i=r(5155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,o=n.useMemo(()=>a,Object.values(a));return(0,i.jsx)(r.Provider,{value:o,children:t})};return a.displayName=e+"Provider",[a,function(i){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return a.scopeName=e,[function(t,a){let o=n.createContext(a),s=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[s]||o,h=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(d.Provider,{value:h,children:a})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||o,d=n.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(a,...t)]}},6090:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Ring=void 0;let n=r(8949),i=r(3306),a=r(6394),o=r(6878),s=2*Math.PI;class l extends i.Shape{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.innerRadius(),0,s,!1),e.moveTo(this.outerRadius(),0),e.arc(0,0,this.outerRadius(),s,0,!0),e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}}t.Ring=l,l.prototype.className="Ring",l.prototype._centroid=!0,l.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,o._registerNode)(l),n.Factory.addGetterSetter(l,"innerRadius",0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(l,"outerRadius",0,(0,a.getNumberValidator)())},6101:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>a});var n=r(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function o(...e){return n.useCallback(a(...e),e)}},6345:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Animation=void 0;let n=r(6878),i=r(6509),a=n.glob.performance&&n.glob.performance.now?function(){return n.glob.performance.now()}:function(){return new Date().getTime()};class o{constructor(e,t){this.id=o.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:a(),frameRate:0},this.func=e,this.setLayers(t)}setLayers(e){let t=[];return e&&(t=Array.isArray(e)?e:[e]),this.layers=t,this}getLayers(){return this.layers}addLayer(e){let t=this.layers,r=t.length;for(let n=0;n<r;n++)if(t[n]._id===e._id)return!1;return this.layers.push(e),!0}isRunning(){let e=o.animations,t=e.length;for(let r=0;r<t;r++)if(e[r].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=a(),o._addAnimation(this),this}stop(){return o._removeAnimation(this),this}_updateFrameObject(e){this.frame.timeDiff=e-this.frame.lastTime,this.frame.lastTime=e,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(e){this.animations.push(e),this._handleAnimation()}static _removeAnimation(e){let t=e.id,r=this.animations,n=r.length;for(let e=0;e<n;e++)if(r[e].id===t){this.animations.splice(e,1);break}}static _runFrames(){let e={},t=this.animations;for(let r=0;r<t.length;r++){let n=t[r],i=n.layers,o=n.func;n._updateFrameObject(a());let s=i.length;if(!o||!1!==o.call(n,n.frame))for(let t=0;t<s;t++){let r=i[t];void 0!==r._id&&(e[r._id]=r)}}for(let t in e)e.hasOwnProperty(t)&&e[t].batchDraw()}static _animationLoop(){o.animations.length?(o._runFrames(),i.Util.requestAnimFrame(o._animationLoop)):o.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,i.Util.requestAnimFrame(this._animationLoop))}}t.Animation=o,o.animations=[],o.animIdCounter=0,o.animRunning=!1},6394:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RGBComponent=function(e){return e>255?255:e<0?0:Math.round(e)},t.alphaComponent=function(e){return e>1?1:e<1e-4?1e-4:e},t.getNumberValidator=function(){if(n.Konva.isUnminified)return function(e,t){return i.Util._isNumber(e)||i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a number.'),e}},t.getNumberOrArrayOfNumbersValidator=function(e){if(n.Konva.isUnminified)return function(t,r){let n=i.Util._isNumber(t),o=i.Util._isArray(t)&&t.length==e;return n||o||i.Util.warn(a(t)+' is a not valid value for "'+r+'" attribute. The value should be a number or Array<number>('+e+")"),t}},t.getNumberOrAutoValidator=function(){if(n.Konva.isUnminified)return function(e,t){let r=i.Util._isNumber(e),n="auto"===e;return r||n||i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a number or "auto".'),e}},t.getStringValidator=function(){if(n.Konva.isUnminified)return function(e,t){return i.Util._isString(e)||i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a string.'),e}},t.getStringOrGradientValidator=function(){if(n.Konva.isUnminified)return function(e,t){let r=i.Util._isString(e),n="[object CanvasGradient]"===Object.prototype.toString.call(e)||e&&e.addColorStop;return r||n||i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a string or a native gradient.'),e}},t.getFunctionValidator=function(){if(n.Konva.isUnminified)return function(e,t){return i.Util._isFunction(e)||i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a function.'),e}},t.getNumberArrayValidator=function(){if(n.Konva.isUnminified)return function(e,t){let r=Int8Array?Object.getPrototypeOf(Int8Array):null;return r&&e instanceof r||(i.Util._isArray(e)?e.forEach(function(e){i.Util._isNumber(e)||i.Util.warn('"'+t+'" attribute has non numeric element '+e+". Make sure that all elements are numbers.")}):i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a array of numbers.')),e}},t.getBooleanValidator=function(){if(n.Konva.isUnminified)return function(e,t){return!0!==e&&!1!==e&&i.Util.warn(a(e)+' is a not valid value for "'+t+'" attribute. The value should be a boolean.'),e}},t.getComponentValidator=function(e){if(n.Konva.isUnminified)return function(t,r){return null==t||i.Util.isObject(t)||i.Util.warn(a(t)+' is a not valid value for "'+r+'" attribute. The value should be an object with properties '+e),t}};let n=r(6878),i=r(6509);function a(e){return i.Util._isString(e)?'"'+e+'"':"[object Number]"===Object.prototype.toString.call(e)||i.Util._isBoolean(e)?e:Object.prototype.toString.call(e)}},6466:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RegularPolygon=void 0;let n=r(8949),i=r(3306),a=r(6394),o=r(6878);class s extends i.Shape{_sceneFunc(e){let t=this._getPoints();e.beginPath(),e.moveTo(t[0].x,t[0].y);for(let r=1;r<t.length;r++)e.lineTo(t[r].x,t[r].y);e.closePath(),e.fillStrokeShape(this)}_getPoints(){let e=this.attrs.sides,t=this.attrs.radius||0,r=[];for(let n=0;n<e;n++)r.push({x:t*Math.sin(2*n*Math.PI/e),y:-1*t*Math.cos(2*n*Math.PI/e)});return r}getSelfRect(){let e=this._getPoints(),t=e[0].x,r=e[0].y,n=e[0].x,i=e[0].y;return e.forEach(e=>{t=Math.min(t,e.x),r=Math.max(r,e.x),n=Math.min(n,e.y),i=Math.max(i,e.y)}),{x:t,y:n,width:r-t,height:i-n}}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(e){this.radius(e/2)}setHeight(e){this.radius(e/2)}}t.RegularPolygon=s,s.prototype.className="RegularPolygon",s.prototype._centroid=!0,s.prototype._attrsAffectingSize=["radius"],(0,o._registerNode)(s),n.Factory.addGetterSetter(s,"radius",0,(0,a.getNumberValidator)()),n.Factory.addGetterSetter(s,"sides",0,(0,a.getNumberValidator)())},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6500:(e,t)=>{t.ConcurrentRoot=1,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},6509:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Util=t.Transform=void 0;let n=r(6878);class i{constructor(e=[1,0,0,1,0,0]){this.dirty=!1,this.m=e&&e.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new i(this.m)}copyInto(e){e.m[0]=this.m[0],e.m[1]=this.m[1],e.m[2]=this.m[2],e.m[3]=this.m[3],e.m[4]=this.m[4],e.m[5]=this.m[5]}point(e){let t=this.m;return{x:t[0]*e.x+t[2]*e.y+t[4],y:t[1]*e.x+t[3]*e.y+t[5]}}translate(e,t){return this.m[4]+=this.m[0]*e+this.m[2]*t,this.m[5]+=this.m[1]*e+this.m[3]*t,this}scale(e,t){return this.m[0]*=e,this.m[1]*=e,this.m[2]*=t,this.m[3]*=t,this}rotate(e){let t=Math.cos(e),r=Math.sin(e),n=this.m[0]*t+this.m[2]*r,i=this.m[1]*t+this.m[3]*r,a=-(this.m[0]*r)+this.m[2]*t,o=-(this.m[1]*r)+this.m[3]*t;return this.m[0]=n,this.m[1]=i,this.m[2]=a,this.m[3]=o,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(e,t){let r=this.m[0]+this.m[2]*t,n=this.m[1]+this.m[3]*t,i=this.m[2]+this.m[0]*e,a=this.m[3]+this.m[1]*e;return this.m[0]=r,this.m[1]=n,this.m[2]=i,this.m[3]=a,this}multiply(e){let t=this.m[0]*e.m[0]+this.m[2]*e.m[1],r=this.m[1]*e.m[0]+this.m[3]*e.m[1],n=this.m[0]*e.m[2]+this.m[2]*e.m[3],i=this.m[1]*e.m[2]+this.m[3]*e.m[3],a=this.m[0]*e.m[4]+this.m[2]*e.m[5]+this.m[4],o=this.m[1]*e.m[4]+this.m[3]*e.m[5]+this.m[5];return this.m[0]=t,this.m[1]=r,this.m[2]=n,this.m[3]=i,this.m[4]=a,this.m[5]=o,this}invert(){let e=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),t=this.m[3]*e,r=-this.m[1]*e,n=-this.m[2]*e,i=this.m[0]*e,a=e*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),o=e*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=t,this.m[1]=r,this.m[2]=n,this.m[3]=i,this.m[4]=a,this.m[5]=o,this}getMatrix(){return this.m}decompose(){let e=this.m[0],r=this.m[1],n=this.m[2],i=this.m[3],a=this.m[4],o=this.m[5],s=e*i-r*n,l={x:a,y:o,rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!=e||0!=r){let t=Math.sqrt(e*e+r*r);l.rotation=r>0?Math.acos(e/t):-Math.acos(e/t),l.scaleX=t,l.scaleY=s/t,l.skewX=(e*n+r*i)/s,l.skewY=0}else if(0!=n||0!=i){let t=Math.sqrt(n*n+i*i);l.rotation=Math.PI/2-(i>0?Math.acos(-n/t):-Math.acos(n/t)),l.scaleX=s/t,l.scaleY=t,l.skewX=0,l.skewY=(e*n+r*i)/s}return l.rotation=t.Util._getRotation(l.rotation),l}}t.Transform=i;let a=Math.PI/180,o=180/Math.PI,s="Konva error: ",l={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},d=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/,h=[],c="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||function(e){setTimeout(e,60)};t.Util={_isElement:e=>!!(e&&1==e.nodeType),_isFunction:e=>!!(e&&e.constructor&&e.call&&e.apply),_isPlainObject:e=>!!e&&e.constructor===Object,_isArray:e=>"[object Array]"===Object.prototype.toString.call(e),_isNumber:e=>"[object Number]"===Object.prototype.toString.call(e)&&!isNaN(e)&&isFinite(e),_isString:e=>"[object String]"===Object.prototype.toString.call(e),_isBoolean:e=>"[object Boolean]"===Object.prototype.toString.call(e),isObject:e=>e instanceof Object,isValidSelector(e){if("string"!=typeof e)return!1;let t=e[0];return"#"===t||"."===t||t===t.toUpperCase()},_sign:e=>0===e||e>0?1:-1,requestAnimFrame(e){h.push(e),1===h.length&&c(function(){let e=h;h=[],e.forEach(function(e){e()})})},createCanvasElement(){let e=document.createElement("canvas");try{e.style=e.style||{}}catch(e){}return e},createImageElement:()=>document.createElement("img"),_isInDocument(e){for(;e=e.parentNode;)if(e==document)return!0;return!1},_urlToImage(e,r){let n=t.Util.createImageElement();n.onload=function(){r(n)},n.src=e},_rgbToHex:(e,t,r)=>(0x1000000+(e<<16)+(t<<8)+r).toString(16).slice(1),_hexToRgb(e){let t=parseInt(e=e.replace("#",""),16);return{r:t>>16&255,g:t>>8&255,b:255&t}},getRandomColor(){let e=(0xffffff*Math.random()|0).toString(16);for(;e.length<6;)e="0"+e;return"#"+e},getRGB(e){let t;return e in l?{r:(t=l[e])[0],g:t[1],b:t[2]}:"#"===e[0]?this._hexToRgb(e.substring(1)):"rgb("===e.substr(0,4)?{r:parseInt((t=d.exec(e.replace(/ /g,"")))[1],10),g:parseInt(t[2],10),b:parseInt(t[3],10)}:{r:0,g:0,b:0}},colorToRGBA:e=>(e=e||"black",t.Util._namedColorToRBA(e)||t.Util._hex3ColorToRGBA(e)||t.Util._hex4ColorToRGBA(e)||t.Util._hex6ColorToRGBA(e)||t.Util._hex8ColorToRGBA(e)||t.Util._rgbColorToRGBA(e)||t.Util._rgbaColorToRGBA(e)||t.Util._hslColorToRGBA(e)),_namedColorToRBA(e){let t=l[e.toLowerCase()];return t?{r:t[0],g:t[1],b:t[2],a:1}:null},_rgbColorToRGBA(e){if(0===e.indexOf("rgb(")){let t=(e=e.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:t[0],g:t[1],b:t[2],a:1}}},_rgbaColorToRGBA(e){if(0===e.indexOf("rgba(")){let t=(e=e.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map((e,t)=>"%"===e.slice(-1)?3===t?parseInt(e)/100:parseInt(e)/100*255:Number(e));return{r:t[0],g:t[1],b:t[2],a:t[3]}}},_hex8ColorToRGBA(e){if("#"===e[0]&&9===e.length)return{r:parseInt(e.slice(1,3),16),g:parseInt(e.slice(3,5),16),b:parseInt(e.slice(5,7),16),a:parseInt(e.slice(7,9),16)/255}},_hex6ColorToRGBA(e){if("#"===e[0]&&7===e.length)return{r:parseInt(e.slice(1,3),16),g:parseInt(e.slice(3,5),16),b:parseInt(e.slice(5,7),16),a:1}},_hex4ColorToRGBA(e){if("#"===e[0]&&5===e.length)return{r:parseInt(e[1]+e[1],16),g:parseInt(e[2]+e[2],16),b:parseInt(e[3]+e[3],16),a:parseInt(e[4]+e[4],16)/255}},_hex3ColorToRGBA(e){if("#"===e[0]&&4===e.length)return{r:parseInt(e[1]+e[1],16),g:parseInt(e[2]+e[2],16),b:parseInt(e[3]+e[3],16),a:1}},_hslColorToRGBA(e){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(e)){let t,r,n,[i,...a]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(e),o=Number(a[0])/360,s=Number(a[1])/100,l=Number(a[2])/100;if(0===s)return{r:Math.round(n=255*l),g:Math.round(n),b:Math.round(n),a:1};t=l<.5?l*(1+s):l+s-l*s;let d=2*l-t,h=[0,0,0];for(let e=0;e<3;e++)(r=o+-(1/3*(e-1)))<0&&r++,r>1&&r--,n=6*r<1?d+(t-d)*6*r:2*r<1?t:3*r<2?d+(t-d)*(2/3-r)*6:d,h[e]=255*n;return{r:Math.round(h[0]),g:Math.round(h[1]),b:Math.round(h[2]),a:1}}},haveIntersection:(e,t)=>!(t.x>e.x+e.width||t.x+t.width<e.x||t.y>e.y+e.height||t.y+t.height<e.y),cloneObject(e){let t={};for(let r in e)this._isPlainObject(e[r])?t[r]=this.cloneObject(e[r]):this._isArray(e[r])?t[r]=this.cloneArray(e[r]):t[r]=e[r];return t},cloneArray:e=>e.slice(0),degToRad:e=>e*a,radToDeg:e=>e*o,_degToRad:e=>(t.Util.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),t.Util.degToRad(e)),_radToDeg:e=>(t.Util.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),t.Util.radToDeg(e)),_getRotation:e=>n.Konva.angleDeg?t.Util.radToDeg(e):e,_capitalize:e=>e.charAt(0).toUpperCase()+e.slice(1),throw(e){throw Error(s+e)},error(e){console.error(s+e)},warn(e){n.Konva.showWarnings&&console.warn("Konva warning: "+e)},each(e,t){for(let r in e)t(r,e[r])},_inRange:(e,t,r)=>t<=e&&e<r,_getProjectionToSegment(e,t,r,n,i,a){let o,s,l,d=(e-r)*(e-r)+(t-n)*(t-n);if(0==d)o=e,s=t,l=(i-r)*(i-r)+(a-n)*(a-n);else{let h=((i-e)*(r-e)+(a-t)*(n-t))/d;h<0?(o=e,s=t,l=(e-i)*(e-i)+(t-a)*(t-a)):h>1?(o=r,s=n,l=(r-i)*(r-i)+(n-a)*(n-a)):l=((o=e+h*(r-e))-i)*(o-i)+((s=t+h*(n-t))-a)*(s-a)}return[o,s,l]},_getProjectionToLine(e,r,n){let i=t.Util.cloneObject(e),a=Number.MAX_VALUE;return r.forEach(function(o,s){if(!n&&s===r.length-1)return;let l=r[(s+1)%r.length],d=t.Util._getProjectionToSegment(o.x,o.y,l.x,l.y,e.x,e.y),h=d[0],c=d[1],u=d[2];u<a&&(i.x=h,i.y=c,a=u)}),i},_prepareArrayForTween(e,r,n){let i=[],a=[];if(e.length>r.length){let t=r;r=e,e=t}for(let t=0;t<e.length;t+=2)i.push({x:e[t],y:e[t+1]});for(let e=0;e<r.length;e+=2)a.push({x:r[e],y:r[e+1]});let o=[];return a.forEach(function(e){let r=t.Util._getProjectionToLine(e,i,n);o.push(r.x),o.push(r.y)}),o},_prepareToStringify(e){let r;for(let n in e.visitedByCircularReferenceRemoval=!0,e)if(e.hasOwnProperty(n)&&e[n]&&"object"==typeof e[n]){if(r=Object.getOwnPropertyDescriptor(e,n),e[n].visitedByCircularReferenceRemoval||t.Util._isElement(e[n]))if(!r.configurable)return null;else delete e[n];else if(null===t.Util._prepareToStringify(e[n]))if(!r.configurable)return null;else delete e[n]}return delete e.visitedByCircularReferenceRemoval,e},_assign(e,t){for(let r in t)e[r]=t[r];return e},_getFirstPointerId:e=>e.touches?e.changedTouches[0].identifier:e.pointerId||999,releaseCanvas(...e){n.Konva.releaseCanvasOnDestroy&&e.forEach(e=>{e.width=0,e.height=0})},drawRoundedRectPath(e,t,r,n){let i=0,a=0,o=0,s=0;"number"==typeof n?i=a=o=s=Math.min(n,t/2,r/2):(i=Math.min(n[0]||0,t/2,r/2),a=Math.min(n[1]||0,t/2,r/2),s=Math.min(n[2]||0,t/2,r/2),o=Math.min(n[3]||0,t/2,r/2)),e.moveTo(i,0),e.lineTo(t-a,0),e.arc(t-a,a,a,3*Math.PI/2,0,!1),e.lineTo(t,r-s),e.arc(t-s,r-s,s,0,Math.PI/2,!1),e.lineTo(o,r),e.arc(o,r-o,o,Math.PI/2,Math.PI,!1),e.lineTo(0,i),e.arc(i,i,i,Math.PI,3*Math.PI/2,!1)}}},6522:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HSV=void 0;let n=r(8949),i=r(913),a=r(6394);t.HSV=function(e){let t=e.data,r=t.length,n=Math.pow(2,this.value()),i=Math.pow(2,this.saturation()),a=Math.abs(this.hue()+360)%360,o=n*i*Math.cos(a*Math.PI/180),s=n*i*Math.sin(a*Math.PI/180),l=.299*n+.701*o+.167*s,d=.587*n-.587*o+.33*s,h=.114*n-.114*o-.497*s,c=.299*n-.299*o-.328*s,u=.587*n+.413*o+.035*s,f=.114*n-.114*o+.293*s,p=.299*n-.3*o+1.25*s,g=.587*n-.586*o-1.05*s,m=.114*n+.886*o-.2*s;for(let e=0;e<r;e+=4){let r=t[e+0],n=t[e+1],i=t[e+2],a=t[e+3];t[e+0]=l*r+d*n+h*i,t[e+1]=c*r+u*n+f*i,t[e+2]=p*r+g*n+m*i,t[e+3]=a}},n.Factory.addGetterSetter(i.Node,"hue",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"saturation",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"value",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(2115);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=a(e,n)),t&&(i.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6659:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.t2length=t.getQuadraticArcLength=t.getCubicArcLength=t.binomialCoefficients=t.cValues=t.tValues=void 0,t.tValues=[[],[],[-.5773502691896257,.5773502691896257],[0,-.7745966692414834,.7745966692414834],[-.33998104358485626,.33998104358485626,-.8611363115940526,.8611363115940526],[0,-.5384693101056831,.5384693101056831,-.906179845938664,.906179845938664],[.6612093864662645,-.6612093864662645,-.2386191860831969,.2386191860831969,-.932469514203152,.932469514203152],[0,.4058451513773972,-.4058451513773972,-.7415311855993945,.7415311855993945,-.9491079123427585,.9491079123427585],[-.1834346424956498,.1834346424956498,-.525532409916329,.525532409916329,-.7966664774136267,.7966664774136267,-.9602898564975363,.9602898564975363],[0,-.8360311073266358,.8360311073266358,-.9681602395076261,.9681602395076261,-.3242534234038089,.3242534234038089,-.6133714327005904,.6133714327005904],[-.14887433898163122,.14887433898163122,-.4333953941292472,.4333953941292472,-.6794095682990244,.6794095682990244,-.8650633666889845,.8650633666889845,-.9739065285171717,.9739065285171717],[0,-.26954315595234496,.26954315595234496,-.5190961292068118,.5190961292068118,-.7301520055740494,.7301520055740494,-.8870625997680953,.8870625997680953,-.978228658146057,.978228658146057],[-.1252334085114689,.1252334085114689,-.3678314989981802,.3678314989981802,-.5873179542866175,.5873179542866175,-.7699026741943047,.7699026741943047,-.9041172563704749,.9041172563704749,-.9815606342467192,.9815606342467192],[0,-.2304583159551348,.2304583159551348,-.44849275103644687,.44849275103644687,-.6423493394403402,.6423493394403402,-.8015780907333099,.8015780907333099,-.9175983992229779,.9175983992229779,-.9841830547185881,.9841830547185881],[-.10805494870734367,.10805494870734367,-.31911236892788974,.31911236892788974,-.5152486363581541,.5152486363581541,-.6872929048116855,.6872929048116855,-.827201315069765,.827201315069765,-.9284348836635735,.9284348836635735,-.9862838086968123,.9862838086968123],[0,-.20119409399743451,.20119409399743451,-.3941513470775634,.3941513470775634,-.5709721726085388,.5709721726085388,-.7244177313601701,.7244177313601701,-.8482065834104272,.8482065834104272,-.937273392400706,.937273392400706,-.9879925180204854,.9879925180204854],[-.09501250983763744,.09501250983763744,-.2816035507792589,.2816035507792589,-.45801677765722737,.45801677765722737,-.6178762444026438,.6178762444026438,-.755404408355003,.755404408355003,-.8656312023878318,.8656312023878318,-.9445750230732326,.9445750230732326,-.9894009349916499,.9894009349916499],[0,-.17848418149584785,.17848418149584785,-.3512317634538763,.3512317634538763,-.5126905370864769,.5126905370864769,-.6576711592166907,.6576711592166907,-.7815140038968014,.7815140038968014,-.8802391537269859,.8802391537269859,-.9506755217687678,.9506755217687678,-.9905754753144174,.9905754753144174],[-.0847750130417353,.0847750130417353,-.2518862256915055,.2518862256915055,-.41175116146284263,.41175116146284263,-.5597708310739475,.5597708310739475,-.6916870430603532,.6916870430603532,-.8037049589725231,.8037049589725231,-.8926024664975557,.8926024664975557,-.9558239495713977,.9558239495713977,-.9915651684209309,.9915651684209309],[0,-.16035864564022537,.16035864564022537,-.31656409996362983,.31656409996362983,-.46457074137596094,.46457074137596094,-.600545304661681,.600545304661681,-.7209661773352294,.7209661773352294,-.8227146565371428,.8227146565371428,-.9031559036148179,.9031559036148179,-.96020815213483,.96020815213483,-.9924068438435844,.9924068438435844],[-.07652652113349734,.07652652113349734,-.22778585114164507,.22778585114164507,-.37370608871541955,.37370608871541955,-.5108670019508271,.5108670019508271,-.636053680726515,.636053680726515,-.7463319064601508,.7463319064601508,-.8391169718222188,.8391169718222188,-.912234428251326,.912234428251326,-.9639719272779138,.9639719272779138,-.9931285991850949,.9931285991850949],[0,-.1455618541608951,.1455618541608951,-.2880213168024011,.2880213168024011,-.4243421202074388,.4243421202074388,-.5516188358872198,.5516188358872198,-.6671388041974123,.6671388041974123,-.7684399634756779,.7684399634756779,-.8533633645833173,.8533633645833173,-.9200993341504008,.9200993341504008,-.9672268385663063,.9672268385663063,-.9937521706203895,.9937521706203895],[-.06973927331972223,.06973927331972223,-.20786042668822127,.20786042668822127,-.34193582089208424,.34193582089208424,-.469355837986757,.469355837986757,-.5876404035069116,.5876404035069116,-.6944872631866827,.6944872631866827,-.7878168059792081,.7878168059792081,-.8658125777203002,.8658125777203002,-.926956772187174,.926956772187174,-.9700604978354287,.9700604978354287,-.9942945854823992,.9942945854823992],[0,-.1332568242984661,.1332568242984661,-.26413568097034495,.26413568097034495,-.3903010380302908,.3903010380302908,-.5095014778460075,.5095014778460075,-.6196098757636461,.6196098757636461,-.7186613631319502,.7186613631319502,-.8048884016188399,.8048884016188399,-.8767523582704416,.8767523582704416,-.9329710868260161,.9329710868260161,-.9725424712181152,.9725424712181152,-.9947693349975522,.9947693349975522],[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213]],t.cValues=[[],[],[1,1],[.8888888888888888,.5555555555555556,.5555555555555556],[.6521451548625461,.6521451548625461,.34785484513745385,.34785484513745385],[.5688888888888889,.47862867049936647,.47862867049936647,.23692688505618908,.23692688505618908],[.3607615730481386,.3607615730481386,.46791393457269104,.46791393457269104,.17132449237917036,.17132449237917036],[.4179591836734694,.3818300505051189,.3818300505051189,.27970539148927664,.27970539148927664,.1294849661688697,.1294849661688697],[.362683783378362,.362683783378362,.31370664587788727,.31370664587788727,.22238103445337448,.22238103445337448,.10122853629037626,.10122853629037626],[.3302393550012598,.1806481606948574,.1806481606948574,.08127438836157441,.08127438836157441,.31234707704000286,.31234707704000286,.26061069640293544,.26061069640293544],[.29552422471475287,.29552422471475287,.26926671930999635,.26926671930999635,.21908636251598204,.21908636251598204,.1494513491505806,.1494513491505806,.06667134430868814,.06667134430868814],[.2729250867779006,.26280454451024665,.26280454451024665,.23319376459199048,.23319376459199048,.18629021092773426,.18629021092773426,.1255803694649046,.1255803694649046,.05566856711617366,.05566856711617366],[.24914704581340277,.24914704581340277,.2334925365383548,.2334925365383548,.20316742672306592,.20316742672306592,.16007832854334622,.16007832854334622,.10693932599531843,.10693932599531843,.04717533638651183,.04717533638651183],[.2325515532308739,.22628318026289723,.22628318026289723,.2078160475368885,.2078160475368885,.17814598076194574,.17814598076194574,.13887351021978725,.13887351021978725,.09212149983772845,.09212149983772845,.04048400476531588,.04048400476531588],[.2152638534631578,.2152638534631578,.2051984637212956,.2051984637212956,.18553839747793782,.18553839747793782,.15720316715819355,.15720316715819355,.12151857068790319,.12151857068790319,.08015808715976021,.08015808715976021,.03511946033175186,.03511946033175186],[.2025782419255613,.19843148532711158,.19843148532711158,.1861610000155622,.1861610000155622,.16626920581699392,.16626920581699392,.13957067792615432,.13957067792615432,.10715922046717194,.10715922046717194,.07036604748810812,.07036604748810812,.03075324199611727,.03075324199611727],[.1894506104550685,.1894506104550685,.18260341504492358,.18260341504492358,.16915651939500254,.16915651939500254,.14959598881657674,.14959598881657674,.12462897125553388,.12462897125553388,.09515851168249279,.09515851168249279,.062253523938647894,.062253523938647894,.027152459411754096,.027152459411754096],[.17944647035620653,.17656270536699264,.17656270536699264,.16800410215645004,.16800410215645004,.15404576107681028,.15404576107681028,.13513636846852548,.13513636846852548,.11188384719340397,.11188384719340397,.08503614831717918,.08503614831717918,.0554595293739872,.0554595293739872,.02414830286854793,.02414830286854793],[.1691423829631436,.1691423829631436,.16427648374583273,.16427648374583273,.15468467512626524,.15468467512626524,.14064291467065065,.14064291467065065,.12255520671147846,.12255520671147846,.10094204410628717,.10094204410628717,.07642573025488905,.07642573025488905,.0497145488949698,.0497145488949698,.02161601352648331,.02161601352648331],[.1610544498487837,.15896884339395434,.15896884339395434,.15276604206585967,.15276604206585967,.1426067021736066,.1426067021736066,.12875396253933621,.12875396253933621,.11156664554733399,.11156664554733399,.09149002162245,.09149002162245,.06904454273764123,.06904454273764123,.0448142267656996,.0448142267656996,.019461788229726478,.019461788229726478],[.15275338713072584,.15275338713072584,.14917298647260374,.14917298647260374,.14209610931838204,.14209610931838204,.13168863844917664,.13168863844917664,.11819453196151841,.11819453196151841,.10193011981724044,.10193011981724044,.08327674157670475,.08327674157670475,.06267204833410907,.06267204833410907,.04060142980038694,.04060142980038694,.017614007139152118,.017614007139152118],[.14608113364969041,.14452440398997005,.14452440398997005,.13988739479107315,.13988739479107315,.13226893863333747,.13226893863333747,.12183141605372853,.12183141605372853,.10879729916714838,.10879729916714838,.09344442345603386,.09344442345603386,.0761001136283793,.0761001136283793,.057134425426857205,.057134425426857205,.036953789770852494,.036953789770852494,.016017228257774335,.016017228257774335],[.13925187285563198,.13925187285563198,.13654149834601517,.13654149834601517,.13117350478706238,.13117350478706238,.12325237681051242,.12325237681051242,.11293229608053922,.11293229608053922,.10041414444288096,.10041414444288096,.08594160621706773,.08594160621706773,.06979646842452049,.06979646842452049,.052293335152683286,.052293335152683286,.03377490158481415,.03377490158481415,.0146279952982722,.0146279952982722],[.13365457218610619,.1324620394046966,.1324620394046966,.12890572218808216,.12890572218808216,.12304908430672953,.12304908430672953,.11499664022241136,.11499664022241136,.10489209146454141,.10489209146454141,.09291576606003515,.09291576606003515,.07928141177671895,.07928141177671895,.06423242140852585,.06423242140852585,.04803767173108467,.04803767173108467,.030988005856979445,.030988005856979445,.013411859487141771,.013411859487141771],[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872]],t.binomialCoefficients=[[1],[1,1],[1,2,1],[1,3,3,1]],t.getCubicArcLength=(e,n,i)=>{let a,o,s=i/2;a=0;for(let i=0;i<20;i++)o=s*t.tValues[20][i]+s,a+=t.cValues[20][i]*function(e,t,n){let i=r(1,n,e),a=r(1,n,t);return Math.sqrt(i*i+a*a)}(e,n,o);return s*a},t.getQuadraticArcLength=(e,t,r)=>{void 0===r&&(r=1);let n=e[0]-2*e[1]+e[2],i=t[0]-2*t[1]+t[2],a=2*e[1]-2*e[0],o=2*t[1]-2*t[0],s=4*(n*n+i*i);if(0===s)return r*Math.sqrt(Math.pow(e[2]-e[0],2)+Math.pow(t[2]-t[0],2));let l=4*(n*a+i*o)/(2*s),d=r+l,h=(a*a+o*o)/s-l*l,c=d*d+h>0?Math.sqrt(d*d+h):0,u=l*l+h>0?Math.sqrt(l*l+h):0,f=l+Math.sqrt(l*l+h)!==0?h*Math.log(Math.abs((d+c)/(l+u))):0;return Math.sqrt(s)/2*(d*c-l*u+f)};let r=(e,n,i)=>{let a,o,s=i.length-1;if(0===s)return 0;if(0===e){o=0;for(let e=0;e<=s;e++)o+=t.binomialCoefficients[s][e]*Math.pow(1-n,s-e)*Math.pow(n,e)*i[e];return o}a=Array(s);for(let e=0;e<s;e++)a[e]=s*(i[e+1]-i[e]);return r(e-1,n,a)};t.t2length=(e,t,r)=>{let n=1,i=e/t,a=(e-r(i))/t,o=0;for(;n>.001;){let s=Math.abs(e-r(i+a))/t;if(s<n)n=s,i+=a;else{let o=Math.abs(e-r(i-a))/t;o<n?(n=o,i-=a):a/=2}if(++o>500)break}return i}},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},useLinkStatus:function(){return y}});let n=r(6966),i=r(5155),a=n._(r(2115)),o=r(2757),s=r(5227),l=r(9818),d=r(6654),h=r(9991),c=r(5929);r(3230);let u=r(4930),f=r(2664),p=r(6634);function g(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function m(e){let t,r,n,[o,m]=(0,a.useOptimistic)(u.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:b,as:x,children:w,prefetch:_=null,passHref:S,replace:C,shallow:k,scroll:P,onClick:A,onMouseEnter:M,onTouchStart:T,legacyBehavior:E=!1,onNavigate:R,ref:D,unstable_dynamicOnHover:N,...F}=e;t=w,E&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let O=a.default.useContext(s.AppRouterContext),L=!1!==_,I=null===_||"auto"===_?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:G,as:j}=a.default.useMemo(()=>{let e=g(b);return{href:e,as:x?g(x):e}},[b,x]);E&&(r=a.default.Children.only(t));let U=E?r&&"object"==typeof r&&r.ref:D,B=a.default.useCallback(e=>(null!==O&&(y.current=(0,u.mountLinkInstance)(e,G,O,I,L,m)),()=>{y.current&&((0,u.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,u.unmountPrefetchableInstance)(e)}),[L,G,O,I,m]),z={ref:(0,d.useMergedRef)(B,U),onClick(e){E||"function"!=typeof A||A(e),E&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),O&&(e.defaultPrevented||function(e,t,r,n,i,o,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,i?"replace":"push",null==o||o,n.current)})}}(e,G,j,y,C,P,R))},onMouseEnter(e){E||"function"!=typeof M||M(e),E&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),O&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){E||"function"!=typeof T||T(e),E&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),O&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,h.isAbsoluteUrl)(j)?z.href=j:E&&!S&&("a"!==r.type||"href"in r.props)||(z.href=(0,c.addBasePath)(j)),n=E?a.default.cloneElement(r,z):(0,i.jsx)("a",{...F,...z,children:t}),(0,i.jsx)(v.Provider,{value:o,children:n})}r(3180);let v=(0,a.createContext)(u.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6878:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._registerNode=t.Konva=t.glob=void 0;let n=Math.PI/180;t.glob=void 0!==r.g?r.g:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},t.Konva={_global:t.glob,version:"9.3.22",isBrowser:"undefined"!=typeof window&&("[object Window]"===({}).toString.call(window)||"[object global]"===({}).toString.call(window)),isUnminified:/param/.test((function(e){}).toString()),dblClickWindow:400,getAngle:e=>t.Konva.angleDeg?e*n:e,enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,_fixTextRendering:!1,pixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:()=>t.Konva.DD.isDragging,isTransforming(){var e;return null==(e=t.Konva.Transformer)?void 0:e.isTransforming()},isDragReady:()=>!!t.Konva.DD.node,releaseCanvasOnDestroy:!0,document:t.glob.document,_injectGlobal(e){t.glob.Konva=e}},t._registerNode=e=>{t.Konva[e.prototype.getClassName()]=e},t.Konva._injectGlobal(t.Konva)},6892:(e,t)=>{function r(e,t){var r=e.length;for(e.push(t);0<r;){var n=r-1>>>1,i=e[n];if(0<a(i,t))e[n]=t,e[r]=i,r=n;else break}}function n(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;for(var n=0,i=e.length,o=i>>>1;n<o;){var s=2*(n+1)-1,l=e[s],d=s+1,h=e[d];if(0>a(l,r))d<i&&0>a(h,l)?(e[n]=h,e[d]=r,n=d):(e[n]=l,e[s]=r,n=s);else if(d<i&&0>a(h,r))e[n]=h,e[d]=r,n=d;else break}}return t}function a(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o,s=performance;t.unstable_now=function(){return s.now()}}else{var l=Date,d=l.now();t.unstable_now=function(){return l.now()-d}}var h=[],c=[],u=1,f=null,p=3,g=!1,m=!1,v=!1,y=!1,b="function"==typeof setTimeout?setTimeout:null,x="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function _(e){for(var t=n(c);null!==t;){if(null===t.callback)i(c);else if(t.startTime<=e)i(c),t.sortIndex=t.expirationTime,r(h,t);else break;t=n(c)}}function S(e){if(v=!1,_(e),!m)if(null!==n(h))m=!0,C||(C=!0,o());else{var t=n(c);null!==t&&D(S,t.startTime-e)}}var C=!1,k=-1,P=5,A=-1;function M(){return!!y||!(t.unstable_now()-A<P)}function T(){if(y=!1,C){var e=t.unstable_now();A=e;var r=!0;try{e:{m=!1,v&&(v=!1,x(k),k=-1),g=!0;var a=p;try{t:{for(_(e),f=n(h);null!==f&&!(f.expirationTime>e&&M());){var s=f.callback;if("function"==typeof s){f.callback=null,p=f.priorityLevel;var l=s(f.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){f.callback=l,_(e),r=!0;break t}f===n(h)&&i(h),_(e)}else i(h);f=n(h)}if(null!==f)r=!0;else{var d=n(c);null!==d&&D(S,d.startTime-e),r=!1}}break e}finally{f=null,p=a,g=!1}}}finally{r?o():C=!1}}}if("function"==typeof w)o=function(){w(T)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,R=E.port2;E.port1.onmessage=T,o=function(){R.postMessage(null)}}else o=function(){b(T,0)};function D(e,r){k=b(function(){e(t.unstable_now())},r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},t.unstable_scheduleCallback=function(e,i,a){var s=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?s+a:s,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=a+l,e={id:u++,callback:i,priorityLevel:e,startTime:a,expirationTime:l,sortIndex:-1},a>s?(e.sortIndex=a,r(c,e),null===n(h)&&e===n(c)&&(v?(x(k),k=-1):v=!0,D(S,a-s))):(e.sortIndex=l,r(h,e),m||g||(m=!0,C||(C=!0,o()))),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}},6955:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Contrast=void 0;let n=r(8949),i=r(913),a=r(6394);t.Contrast=function(e){let t=Math.pow((this.contrast()+100)/100,2),r=e.data,n=r.length,i=150,a=150,o=150;for(let e=0;e<n;e+=4)i=r[e],a=r[e+1],o=r[e+2],i/=255,i-=.5,i*=t,i+=.5,i*=255,a/=255,a-=.5,a*=t,a+=.5,a*=255,o/=255,o-=.5,o*=t,o+=.5,o*=255,i=i<0?0:i>255?255:i,a=a<0?0:a>255?255:a,o=o<0?0:o>255?255:o,r[e]=i,r[e+1]=a,r[e+2]=o},n.Factory.addGetterSetter(i.Node,"contrast",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},7213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7325:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},7328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function i(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function a(e,t,r){var i=n(e,t,"set");if(i.set)i.set.call(e,r);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=r}return r}r.d(t,{N:()=>u});var o,s=r(2115),l=r(6081),d=r(6101),h=r(9708),c=r(5155);function u(e){let t=e+"CollectionProvider",[r,n]=(0,l.A)(t),[i,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:r}=e,n=s.useRef(null),a=s.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:a,collectionRef:n,children:r})};o.displayName=t;let u=e+"CollectionSlot",f=(0,h.TL)(u),p=s.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=a(u,r),o=(0,d.s)(t,i.collectionRef);return(0,c.jsx)(f,{ref:o,children:n})});p.displayName=u;let g=e+"CollectionItemSlot",m="data-radix-collection-item",v=(0,h.TL)(g),y=s.forwardRef((e,t)=>{let{scope:r,children:n,...i}=e,o=s.useRef(null),l=(0,d.s)(t,o),h=a(g,r);return s.useEffect(()=>(h.itemMap.set(o,{ref:o,...i}),()=>void h.itemMap.delete(o))),(0,c.jsx)(v,{...{[m]:""},ref:l,children:n})});return y.displayName=g,[{Provider:o,Slot:p,ItemSlot:y},function(t){let r=a(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var f=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=g(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function g(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return f.get(this)&&(this.has(e)?i(this,o)[i(this,o).indexOf(e)]=e:i(this,o).push(e)),super.set(e,t),this}insert(e,t,r){let n,a=this.has(t),s=i(this,o).length,l=g(e),d=l>=0?l:s+l,h=d<0||d>=s?-1:d;if(h===this.size||a&&h===this.size-1||-1===h)return this.set(t,r),this;let c=this.size+ +!a;l<0&&d++;let u=[...i(this,o)],f=!1;for(let e=d;e<c;e++)if(d===e){let i=u[e];u[e]===t&&(i=u[e+1]),a&&this.delete(t),n=this.get(i),this.set(t,r)}else{f||u[e-1]!==t||(f=!0);let r=u[f?e:e-1],i=n;n=this.get(r),this.delete(r),this.set(r,i)}return this}with(t,r,n){let i=new e(this);return i.insert(t,r,n),i}before(e){let t=i(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let n=i(this,o).indexOf(e);return -1===n?this:this.insert(n,t,r)}after(e){let t=i(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let n=i(this,o).indexOf(e);return -1===n?this:this.insert(n+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return a(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&i(this,o).splice(i(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=p(i(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=p(i(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return i(this,o).indexOf(e)}keyAt(e){return p(i(this,o),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],i=0;for(let e of this)Reflect.apply(t,r,[e,i,this])&&n.push(e),i++;return new e(n)}map(t,r){let n=[],i=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,i,this])]),i++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]=t,a=0,o=null!=i?i:this.at(0);for(let e of this)o=0===a&&1===t.length?e:Reflect.apply(n,this,[o,e,a,this]),a++;return o}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]=t,a=null!=i?i:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);a=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[a,r,e,this])}return a}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i=[...this.entries()];return i.splice(...r),new e(i)}slice(t,r){let n=new e,i=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(i=r-1);for(let e=t;e<=i;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,o,{writable:!0,value:void 0}),a(this,o,[...super.keys()]),f.set(this,!0)}}},7371:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Transformer=void 0;let n=r(6509),i=r(8949),a=r(913),o=r(3306),s=r(3186),l=r(836),d=r(6878),h=r(6394),c=r(6878),u="tr-konva",f=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange","anchorStyleFuncChange"].map(e=>e+`.${u}`).join(" "),p="nodesRect",g=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],m={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135},v="ontouchstart"in d.Konva._global,y=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function b(e,t,r){let n=r.x+(e.x-r.x)*Math.cos(t)-(e.y-r.y)*Math.sin(t),i=r.y+(e.x-r.x)*Math.sin(t)+(e.y-r.y)*Math.cos(t);return{...e,rotation:e.rotation+t,x:n,y:i}}let x=0;class w extends l.Group{constructor(e){super(e),this._movingAnchorName=null,this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(f,this.update),this.getNode()&&this.update()}attachTo(e){return this.setNode(e),this}setNode(e){return n.Util.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([e])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return u+this._id}setNodes(e=[]){this._nodes&&this._nodes.length&&this.detach();let t=e.filter(e=>!e.isAncestorOf(this)||(n.Util.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1));return this._nodes=e=t,1===e.length&&this.useSingleNodeRotation()?this.rotation(e[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach(e=>{let t=()=>{1===this.nodes().length&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),this._transforming||this.isDragging()||this.update()};if(e._attrsAffectingSize.length){let r=e._attrsAffectingSize.map(e=>e+"Change."+this._getEventNamespace()).join(" ");e.on(r,t)}e.on(g.map(e=>e+`.${this._getEventNamespace()}`).join(" "),t),e.on(`absoluteTransformChange.${this._getEventNamespace()}`,t),this._proxyDrag(e)}),this._resetTransformCache(),this.findOne(".top-left")&&this.update(),this}_proxyDrag(e){let t;e.on(`dragstart.${this._getEventNamespace()}`,r=>{t=e.getAbsolutePosition(),this.isDragging()||e===this.findOne(".back")||this.startDrag(r,!1)}),e.on(`dragmove.${this._getEventNamespace()}`,r=>{if(!t)return;let n=e.getAbsolutePosition(),i=n.x-t.x,a=n.y-t.y;this.nodes().forEach(t=>{if(t===e||t.isDragging())return;let n=t.getAbsolutePosition();t.setAbsolutePosition({x:n.x+i,y:n.y+a}),t.startDrag(r)}),t=null})}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach(e=>{e.off("."+this._getEventNamespace())}),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(p),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(p,this.__getNodeRect)}__getNodeShape(e,t=this.rotation(),r){let n=e.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),i=e.getAbsoluteScale(r),a=e.getAbsolutePosition(r),o=n.x*i.x-e.offsetX()*i.x,s=n.y*i.y-e.offsetY()*i.y,l=(d.Konva.getAngle(e.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return b({x:a.x+o*Math.cos(l)+s*Math.sin(-l),y:a.y+s*Math.cos(l)+o*Math.sin(l),width:n.width*i.x,height:n.height*i.y,rotation:l},-d.Konva.getAngle(t),{x:0,y:0})}__getNodeRect(){if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};let e=[];this.nodes().map(t=>{let r=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),n=[{x:r.x,y:r.y},{x:r.x+r.width,y:r.y},{x:r.x+r.width,y:r.y+r.height},{x:r.x,y:r.y+r.height}],i=t.getAbsoluteTransform();n.forEach(function(t){let r=i.point(t);e.push(r)})});let t=new n.Transform;t.rotate(-d.Konva.getAngle(this.rotation()));let r=1/0,i=1/0,a=-1/0,o=-1/0;e.forEach(function(e){let n=t.point(e);void 0===r&&(r=a=n.x,i=o=n.y),r=Math.min(r,n.x),i=Math.min(i,n.y),a=Math.max(a,n.x),o=Math.max(o,n.y)}),t.invert();let s=t.point({x:r,y:i});return{x:s.x,y:s.y,width:a-r,height:o-i,rotation:d.Konva.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),y.forEach(e=>{this._createAnchor(e)}),this._createAnchor("rotater")}_createAnchor(e){let t=new s.Rect({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:e+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:v?10:"auto"}),r=this;t.on("mousedown touchstart",function(e){r._handleMouseDown(e)}),t.on("dragstart",e=>{t.stopDrag(),e.cancelBubble=!0}),t.on("dragend",e=>{e.cancelBubble=!0}),t.on("mouseenter",()=>{let r=function(e,t,r){if("rotater"===e)return r;t+=n.Util.degToRad(m[e]||0);let i=(n.Util.radToDeg(t)%360+360)%360;return n.Util._inRange(i,337.5,360)||n.Util._inRange(i,0,22.5)?"ns-resize":n.Util._inRange(i,22.5,67.5)?"nesw-resize":n.Util._inRange(i,67.5,112.5)?"ew-resize":n.Util._inRange(i,112.5,157.5)?"nwse-resize":n.Util._inRange(i,157.5,202.5)?"ns-resize":n.Util._inRange(i,202.5,247.5)?"nesw-resize":n.Util._inRange(i,247.5,292.5)?"ew-resize":n.Util._inRange(i,292.5,337.5)?"nwse-resize":(n.Util.error("Transformer has unknown angle for cursor detection: "+i),"pointer")}(e,d.Konva.getAngle(this.rotation()),this.rotateAnchorCursor());t.getStage().content&&(t.getStage().content.style.cursor=r),this._cursorChange=!0}),t.on("mouseout",()=>{t.getStage().content&&(t.getStage().content.style.cursor=""),this._cursorChange=!1}),this.add(t)}_createBack(){let e=new o.Shape({name:"back",width:0,height:0,draggable:!0,sceneFunc(e,t){let r=t.getParent(),i=r.padding();e.beginPath(),e.rect(-i,-i,t.width()+2*i,t.height()+2*i),e.moveTo(t.width()/2,-i),r.rotateEnabled()&&r.rotateLineVisible()&&e.lineTo(t.width()/2,-r.rotateAnchorOffset()*n.Util._sign(t.height())-i),e.fillStrokeShape(t)},hitFunc:(e,t)=>{if(!this.shouldOverdrawWholeArea())return;let r=this.padding();e.beginPath(),e.rect(-r,-r,t.width()+2*r,t.height()+2*r),e.fillStrokeShape(t)}});this.add(e),this._proxyDrag(e),e.on("dragstart",e=>{e.cancelBubble=!0}),e.on("dragmove",e=>{e.cancelBubble=!0}),e.on("dragend",e=>{e.cancelBubble=!0}),this.on("dragmove",e=>{this.update()})}_handleMouseDown(e){if(this._transforming)return;this._movingAnchorName=e.target.name().split(" ")[0];let t=this._getNodeRect(),r=t.width,n=t.height,i=Math.sqrt(Math.pow(r,2)+Math.pow(n,2));this.sin=Math.abs(n/i),this.cos=Math.abs(r/i),"undefined"!=typeof window&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;let a=e.target.getAbsolutePosition(),o=e.target.getStage().getPointerPosition();this._anchorDragOffset={x:o.x-a.x,y:o.y-a.y},x++,this._fire("transformstart",{evt:e.evt,target:this.getNode()}),this._nodes.forEach(t=>{t._fire("transformstart",{evt:e.evt,target:t})})}_handleMouseMove(e){let t,r,n,i,a=this.findOne("."+this._movingAnchorName),o=a.getStage();o.setPointersPositions(e);let s=o.getPointerPosition(),l={x:s.x-this._anchorDragOffset.x,y:s.y-this._anchorDragOffset.y},h=a.getAbsolutePosition();this.anchorDragBoundFunc()&&(l=this.anchorDragBoundFunc()(h,l,e)),a.setAbsolutePosition(l);let c=a.getAbsolutePosition();if(h.x===c.x&&h.y===c.y)return;if("rotater"===this._movingAnchorName){let n=this._getNodeRect();t=a.x()-n.width/2;let i=Math.atan2(-(r=-a.y()+n.height/2),t)+Math.PI/2;n.height<0&&(i-=Math.PI);let o=d.Konva.getAngle(this.rotation())+i,s=d.Konva.getAngle(this.rotationSnapTolerance()),l=function(e,t,r){let n=t;for(let i=0;i<e.length;i++){let a=d.Konva.getAngle(e[i]),o=Math.abs(a-t)%(2*Math.PI);Math.min(o,2*Math.PI-o)<r&&(n=a)}return n}(this.rotationSnaps(),o,s)-n.rotation,h=function(e,t){let r={x:e.x+e.width/2*Math.cos(e.rotation)+e.height/2*Math.sin(-e.rotation),y:e.y+e.height/2*Math.cos(e.rotation)+e.width/2*Math.sin(e.rotation)};return b(e,t,r)}(n,l);this._fitNodesInto(h,e);return}let u=this.shiftBehavior();i="inverted"===u?this.keepRatio()&&!e.shiftKey:"none"===u?this.keepRatio():this.keepRatio()||e.shiftKey;let f=this.centeredScaling()||e.altKey;if("top-left"===this._movingAnchorName){if(i){let e=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};n=Math.sqrt(Math.pow(e.x-a.x(),2)+Math.pow(e.y-a.y(),2));let i=this.findOne(".top-left").x()>e.x?-1:1,o=this.findOne(".top-left").y()>e.y?-1:1;t=n*this.cos*i,r=n*this.sin*o,this.findOne(".top-left").x(e.x-t),this.findOne(".top-left").y(e.y-r)}}else if("top-center"===this._movingAnchorName)this.findOne(".top-left").y(a.y());else if("top-right"===this._movingAnchorName){if(i){let e=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};n=Math.sqrt(Math.pow(a.x()-e.x,2)+Math.pow(e.y-a.y(),2));let i=this.findOne(".top-right").x()<e.x?-1:1,o=this.findOne(".top-right").y()>e.y?-1:1;t=n*this.cos*i,r=n*this.sin*o,this.findOne(".top-right").x(e.x+t),this.findOne(".top-right").y(e.y-r)}var p=a.position();this.findOne(".top-left").y(p.y),this.findOne(".bottom-right").x(p.x)}else if("middle-left"===this._movingAnchorName)this.findOne(".top-left").x(a.x());else if("middle-right"===this._movingAnchorName)this.findOne(".bottom-right").x(a.x());else if("bottom-left"===this._movingAnchorName){if(i){let e=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};n=Math.sqrt(Math.pow(e.x-a.x(),2)+Math.pow(a.y()-e.y,2));let i=e.x<a.x()?-1:1,o=a.y()<e.y?-1:1;t=n*this.cos*i,r=n*this.sin*o,a.x(e.x-t),a.y(e.y+r)}p=a.position(),this.findOne(".top-left").x(p.x),this.findOne(".bottom-right").y(p.y)}else if("bottom-center"===this._movingAnchorName)this.findOne(".bottom-right").y(a.y());else if("bottom-right"===this._movingAnchorName){if(i){let e=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};n=Math.sqrt(Math.pow(a.x()-e.x,2)+Math.pow(a.y()-e.y,2));let i=this.findOne(".bottom-right").x()<e.x?-1:1,o=this.findOne(".bottom-right").y()<e.y?-1:1;t=n*this.cos*i,r=n*this.sin*o,this.findOne(".bottom-right").x(e.x+t),this.findOne(".bottom-right").y(e.y+r)}}else console.error(Error("Wrong position argument of selection resizer: "+this._movingAnchorName));if(f=this.centeredScaling()||e.altKey){let e=this.findOne(".top-left"),t=this.findOne(".bottom-right"),r=e.x(),n=e.y(),i=this.getWidth()-t.x(),a=this.getHeight()-t.y();t.move({x:-r,y:-n}),e.move({x:i,y:a})}let g=this.findOne(".top-left").getAbsolutePosition();t=g.x,r=g.y;let m=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),v=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:t,y:r,width:m,height:v,rotation:d.Konva.getAngle(this.rotation())},e)}_handleMouseUp(e){this._removeEvents(e)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(e){var t;if(this._transforming){this._transforming=!1,"undefined"!=typeof window&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));let r=this.getNode();x--,this._fire("transformend",{evt:e,target:r}),null==(t=this.getLayer())||t.batchDraw(),r&&this._nodes.forEach(t=>{var r;t._fire("transformend",{evt:e,target:t}),null==(r=t.getLayer())||r.batchDraw()}),this._movingAnchorName=null}}_fitNodesInto(e,t){let r=this._getNodeRect();if(n.Util._inRange(e.width,-(2*this.padding())-1,1)||n.Util._inRange(e.height,-(2*this.padding())-1,1))return void this.update();let i=new n.Transform;if(i.rotate(d.Konva.getAngle(this.rotation())),this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("left")>=0){let t=i.point({x:-(2*this.padding()),y:0});e.x+=t.x,e.y+=t.y,e.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y}else if(this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("right")>=0){let t=i.point({x:2*this.padding(),y:0});this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.width+=2*this.padding()}if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("top")>=0){let t=i.point({x:0,y:-(2*this.padding())});e.x+=t.x,e.y+=t.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}else if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("bottom")>=0){let t=i.point({x:0,y:2*this.padding()});this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}if(this.boundBoxFunc()){let t=this.boundBoxFunc()(r,e);t?e=t:n.Util.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}let a=new n.Transform;a.translate(r.x,r.y),a.rotate(r.rotation),a.scale(r.width/1e7,r.height/1e7);let o=new n.Transform,s=e.width/1e7,l=e.height/1e7;!1===this.flipEnabled()?(o.translate(e.x,e.y),o.rotate(e.rotation),o.translate(e.width<0?e.width:0,e.height<0?e.height:0),o.scale(Math.abs(s),Math.abs(l))):(o.translate(e.x,e.y),o.rotate(e.rotation),o.scale(s,l));let h=o.multiply(a.invert());this._nodes.forEach(e=>{var t;let r=e.getParent().getAbsoluteTransform(),i=e.getTransform().copy();i.translate(e.offsetX(),e.offsetY());let a=new n.Transform;a.multiply(r.copy().invert()).multiply(h).multiply(r).multiply(i);let o=a.decompose();e.setAttrs(o),null==(t=e.getLayer())||t.batchDraw()}),this.rotation(n.Util._getRotation(e.rotation)),this._nodes.forEach(e=>{this._fire("transform",{evt:t,target:e}),e._fire("transform",{evt:t,target:e})}),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(e,t){this.findOne(e).setAttrs(t)}update(){var e;let t=this._getNodeRect();this.rotation(n.Util._getRotation(t.rotation));let r=t.width,i=t.height,a=this.enabledAnchors(),o=this.resizeEnabled(),s=this.padding(),l=this.anchorSize(),d=this.find("._anchor");d.forEach(e=>{e.setAttrs({width:l,height:l,offsetX:l/2,offsetY:l/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})}),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:l/2+s,offsetY:l/2+s,visible:o&&a.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:r/2,y:0,offsetY:l/2+s,visible:o&&a.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:r,y:0,offsetX:l/2-s,offsetY:l/2+s,visible:o&&a.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:i/2,offsetX:l/2+s,visible:o&&a.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:r,y:i/2,offsetX:l/2-s,visible:o&&a.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:i,offsetX:l/2+s,offsetY:l/2-s,visible:o&&a.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:r/2,y:i,offsetY:l/2-s,visible:o&&a.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:r,y:i,offsetX:l/2-s,offsetY:l/2-s,visible:o&&a.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:r/2,y:-this.rotateAnchorOffset()*n.Util._sign(i)-s,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:r,height:i,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0});let h=this.anchorStyleFunc();h&&d.forEach(e=>{h(e)}),null==(e=this.getLayer())||e.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();let e=this.findOne("."+this._movingAnchorName);e&&e.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),l.Group.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return a.Node.prototype.toObject.call(this)}clone(e){return a.Node.prototype.clone.call(this,e)}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}}t.Transformer=w,w.isTransforming=()=>x>0,w.prototype.className="Transformer",(0,c._registerNode)(w),i.Factory.addGetterSetter(w,"enabledAnchors",y,function(e){return e instanceof Array||n.Util.warn("enabledAnchors value should be an array"),e instanceof Array&&e.forEach(function(e){-1===y.indexOf(e)&&n.Util.warn("Unknown anchor name: "+e+". Available names are: "+y.join(", "))}),e||[]}),i.Factory.addGetterSetter(w,"flipEnabled",!0,(0,h.getBooleanValidator)()),i.Factory.addGetterSetter(w,"resizeEnabled",!0),i.Factory.addGetterSetter(w,"anchorSize",10,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"rotateEnabled",!0),i.Factory.addGetterSetter(w,"rotateLineVisible",!0),i.Factory.addGetterSetter(w,"rotationSnaps",[]),i.Factory.addGetterSetter(w,"rotateAnchorOffset",50,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"rotateAnchorCursor","crosshair"),i.Factory.addGetterSetter(w,"rotationSnapTolerance",5,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"borderEnabled",!0),i.Factory.addGetterSetter(w,"anchorStroke","rgb(0, 161, 255)"),i.Factory.addGetterSetter(w,"anchorStrokeWidth",1,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"anchorFill","white"),i.Factory.addGetterSetter(w,"anchorCornerRadius",0,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"borderStroke","rgb(0, 161, 255)"),i.Factory.addGetterSetter(w,"borderStrokeWidth",1,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"borderDash"),i.Factory.addGetterSetter(w,"keepRatio",!0),i.Factory.addGetterSetter(w,"shiftBehavior","default"),i.Factory.addGetterSetter(w,"centeredScaling",!1),i.Factory.addGetterSetter(w,"ignoreStroke",!1),i.Factory.addGetterSetter(w,"padding",0,(0,h.getNumberValidator)()),i.Factory.addGetterSetter(w,"nodes"),i.Factory.addGetterSetter(w,"node"),i.Factory.addGetterSetter(w,"boundBoxFunc"),i.Factory.addGetterSetter(w,"anchorDragBoundFunc"),i.Factory.addGetterSetter(w,"anchorStyleFunc"),i.Factory.addGetterSetter(w,"shouldOverdrawWholeArea",!1),i.Factory.addGetterSetter(w,"useSingleNodeRotation",!0),i.Factory.backCompat(w,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"})},7498:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Layer=void 0;let n=r(6509),i=r(1248),a=r(913),o=r(8949),s=r(4421),l=r(6394),d=r(3306),h=r(6878),c=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],u=c.length;class f extends i.Container{constructor(e){super(e),this.canvas=new s.SceneCanvas,this.hitCanvas=new s.HitCanvas({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(e){return this.getContext().clear(e),this.getHitCanvas().getContext().clear(e),this}setZIndex(e){super.setZIndex(e);let t=this.getStage();return t&&t.content&&(t.content.removeChild(this.getNativeCanvasElement()),e<t.children.length-1?t.content.insertBefore(this.getNativeCanvasElement(),t.children[e+1].getCanvas()._canvas):t.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){a.Node.prototype.moveToTop.call(this);let e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){if(!a.Node.prototype.moveUp.call(this))return!1;let e=this.getStage();return!!e&&!!e.content&&(e.content.removeChild(this.getNativeCanvasElement()),this.index<e.children.length-1?e.content.insertBefore(this.getNativeCanvasElement(),e.children[this.index+1].getCanvas()._canvas):e.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(a.Node.prototype.moveDown.call(this)){let e=this.getStage();if(e){let t=e.children;e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.insertBefore(this.getNativeCanvasElement(),t[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(a.Node.prototype.moveToBottom.call(this)){let e=this.getStage();if(e){let t=e.children;e.content&&(e.content.removeChild(this.getNativeCanvasElement()),e.content.insertBefore(this.getNativeCanvasElement(),t[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){let e=this.getNativeCanvasElement();return a.Node.prototype.remove.call(this),e&&e.parentNode&&n.Util._isInDocument(e)&&e.parentNode.removeChild(e),this}getStage(){return this.parent}setSize({width:e,height:t}){return this.canvas.setSize(e,t),this.hitCanvas.setSize(e,t),this._setSmoothEnabled(),this}_validateAdd(e){let t=e.getType();"Group"!==t&&"Shape"!==t&&n.Util.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(e){return(e=e||{}).width=e.width||this.getWidth(),e.height=e.height||this.getHeight(),e.x=void 0!==e.x?e.x:this.x(),e.y=void 0!==e.y?e.y:this.y(),a.Node.prototype._toKonvaCanvas.call(this,e)}_checkVisibility(){this.visible()?this.canvas._canvas.style.display="block":this.canvas._canvas.style.display="none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){n.Util.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){n.Util.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,n.Util.requestAnimFrame(()=>{this.draw(),this._waitingForDraw=!1})),this}getIntersection(e){if(!this.isListening()||!this.isVisible())return null;let t=1,r=!1;for(;;){for(let n=0;n<u;n++){let i=c[n],a=this._getIntersection({x:e.x+i.x*t,y:e.y+i.y*t}),o=a.shape;if(o)return o;if(r=!!a.antialiased,!a.antialiased)break}if(!r)return null;t+=1}}_getIntersection(e){let t=this.hitCanvas.pixelRatio,r=this.hitCanvas.context.getImageData(Math.round(e.x*t),Math.round(e.y*t),1,1).data,i=r[3];if(255===i){let e=n.Util._rgbToHex(r[0],r[1],r[2]),t=d.shapes["#"+e];return t?{shape:t}:{antialiased:!0}}return i>0?{antialiased:!0}:{}}drawScene(e,t,r){let n=this.getLayer(),a=e||n&&n.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&a.getContext().clear(),i.Container.prototype.drawScene.call(this,a,t,r),this._fire("draw",{node:this}),this}drawHit(e,t){let r=this.getLayer(),n=e||r&&r.hitCanvas;return r&&r.clearBeforeDraw()&&r.getHitCanvas().getContext().clear(),i.Container.prototype.drawHit.call(this,n,t),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(e){n.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(e)}getHitGraphEnabled(e){return n.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(!this.parent||!this.parent.content)return;let e=this.parent;this.hitCanvas._canvas.parentNode?e.content.removeChild(this.hitCanvas._canvas):e.content.appendChild(this.hitCanvas._canvas)}destroy(){return n.Util.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}}t.Layer=f,f.prototype.nodeType="Layer",(0,h._registerNode)(f),o.Factory.addGetterSetter(f,"imageSmoothingEnabled",!0),o.Factory.addGetterSetter(f,"clearBeforeDraw",!0),o.Factory.addGetterSetter(f,"hitGraphEnabled",!0,(0,l.getBooleanValidator)())},7579:(e,t,r)=>{r.d(t,{_V:()=>eD,Wd:()=>eE,rw:()=>eR,BI:()=>eL,EY:()=>eN,Ge:()=>eF});var n={};r.r(n),r.d(n,{NotPendingTransition:()=>e_,appendChild:()=>q,appendChildToContainer:()=>X,appendInitialChild:()=>C,cancelTimeout:()=>G,clearContainer:()=>es,commitMount:()=>et,commitTextUpdate:()=>ee,commitUpdate:()=>er,createInstance:()=>k,createTextInstance:()=>P,detachDeletedInstance:()=>el,finalizeInitialChildren:()=>A,getChildHostContext:()=>L,getCurrentEventPriority:()=>ed,getCurrentUpdatePriority:()=>ef,getInstanceFromScope:()=>ec,getPublicInstance:()=>M,getRootHostContext:()=>O,hideInstance:()=>en,hideTextInstance:()=>ei,idlePriority:()=>x.unstable_IdlePriority,insertBefore:()=>$,insertInContainerBefore:()=>Z,isPrimaryRenderer:()=>H,maySuspendCommit:()=>ev,noTimeout:()=>B,now:()=>x.unstable_now,preloadInstance:()=>ey,prepareForCommit:()=>T,preparePortalMount:()=>E,prepareScopeUpdate:()=>eh,prepareUpdate:()=>R,removeChild:()=>Q,removeChildFromContainer:()=>J,requestPostPaintCallback:()=>em,resetAfterCommit:()=>D,resetFormInstance:()=>eS,resetTextContent:()=>N,resolveUpdatePriority:()=>ep,run:()=>x.unstable_runWithPriority,scheduleMicrotask:()=>U,scheduleTimeout:()=>I,setCurrentUpdatePriority:()=>eu,shouldAttemptEagerTransition:()=>eg,shouldDeprioritizeSubtree:()=>F,shouldSetTextContent:()=>z,startSuspendingCommit:()=>eb,supportsHydration:()=>Y,supportsMicrotasks:()=>j,supportsMutation:()=>W,supportsPersistence:()=>K,suspendInstance:()=>ex,unhideInstance:()=>ea,unhideTextInstance:()=>eo,waitForCommitToBeReady:()=>ew,warnsIfNotActing:()=>V}),r(5003);var i=r(2115),a=r(2670),o=r.n(a),s=r(5220),l=r.n(s),d=r(1933),h=r(6878);let c={children:!0,ref:!0,key:!0,style:!0,forwardedRef:!0,unstable_applyCache:!0,unstable_applyDrawHitFromCache:!0},u=!1,f=!1,p=".react-konva-event",g=`ReactKonva: You have a Konva node with draggable = true and position defined but no onDragMove or onDragEnd events are handled.
Position of a node will be changed during drag&drop, so you should update state of the react app as well.
Consider to add onDragMove or onDragEnd events.
For more info see: https://github.com/konvajs/react-konva/issues/256
`,m=`ReactKonva: You are using "zIndex" attribute for a Konva node.
react-konva may get confused with ordering. Just define correct order of elements in your render function of a component.
For more info see: https://github.com/konvajs/react-konva/issues/194
`,v={};function y(e,t,r=v){if(!u&&"zIndex"in t&&(console.warn(m),u=!0),!f&&t.draggable){var n=void 0!==t.x||void 0!==t.y,i=t.onDragEnd||t.onDragMove;n&&!i&&(console.warn(g),f=!0)}for(var a in r)if(!c[a]){var o="on"===a.slice(0,2),s=r[a]!==t[a];if(o&&s){var l=a.substr(2).toLowerCase();"content"===l.substr(0,7)&&(l="content"+l.substr(7,1).toUpperCase()+l.substr(8)),e.off(l,r[a])}t.hasOwnProperty(a)||e.setAttr(a,void 0)}var d=t._useStrictMode,h={},x=!1;let w={};for(var a in t)if(!c[a]){var o="on"===a.slice(0,2),_=r[a]!==t[a];if(o&&_){var l=a.substr(2).toLowerCase();"content"===l.substr(0,7)&&(l="content"+l.substr(7,1).toUpperCase()+l.substr(8)),t[a]&&(w[l]=t[a])}!o&&(t[a]!==r[a]||d&&t[a]!==e.getAttr(a))&&(x=!0,h[a]=t[a])}for(var l in x&&(e.setAttrs(h),b(e)),w)e.off(l+p),e.on(l+p,w[l])}function b(e){if(!h.Konva.autoDrawEnabled){var t=e.getLayer()||e.getStage();t&&t.batchDraw()}}var x=r(2407);let w={},_={};o().Node.prototype._applyProps=y;let S=d.DefaultEventPriority;function C(e,t){if("string"==typeof t)return void console.error(`Do not use plain text as child of Konva.Node. You are using text: ${t}`);e.add(t),b(e)}function k(e,t,r){let n=o()[e];n||(console.error(`Konva has no node with the type ${e}. Group will be used instead. If you use minimal version of react-konva, just import required nodes into Konva: "import "konva/lib/shapes/${e}"  If you want to render DOM elements as part of canvas tree take a look into this demo: https://konvajs.github.io/docs/react/DOM_Portal.html`),n=o().Group);let i={},a={};for(var s in t)"ref"!==s&&("on"===s.slice(0,2)?a[s]=t[s]:i[s]=t[s]);let l=new n(i);return y(l,a),l}function P(e,t,r){console.error(`Text components are not supported for now in ReactKonva. Your text is: "${e}"`)}function A(e,t,r){return!1}function M(e){return e}function T(){return null}function E(){return null}function R(e,t,r,n){return _}function D(){}function N(e){}function F(e,t){return!1}function O(){return w}function L(){return w}let I=setTimeout,G=clearTimeout,j=!0,U=e=>{e()},B=-1;function z(e,t){return!1}let H=!1,V=!1,W=!0,K=!1,Y=!1;function q(e,t){t.parent===e?t.moveToTop():e.add(t),b(e)}function X(e,t){t.parent===e?t.moveToTop():e.add(t),b(e)}function $(e,t,r){t._remove(),e.add(t),t.setZIndex(r.getZIndex()),b(e)}function Z(e,t,r){$(e,t,r)}function Q(e,t){t.destroy(),t.off(p),b(e)}function J(e,t){t.destroy(),t.off(p),b(e)}function ee(e,t,r){console.error(`Text components are not yet supported in ReactKonva. You text is: "${r}"`)}function et(e,t,r){}function er(e,t,r,n){y(e,n,r)}function en(e){e.hide(),b(e)}function ei(e){}function ea(e,t){(null==t.visible||t.visible)&&e.show()}function eo(e,t){}function es(e){}function el(){}function ed(){return d.DefaultEventPriority}function eh(){}function ec(){return null}function eu(e){S=e}function ef(){return S}function ep(){return d.DiscreteEventPriority}function eg(){return!1}function em(){}function ev(){return!1}function ey(){return!0}function eb(){}function ex(){}function ew(){return null}let e_=null;function eS(){}function eC(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?i.useLayoutEffect:i.useEffect;let ek=eC(i.createContext(null));class eP extends i.Component{render(){return i.createElement(ek.Provider,{value:this._reactInternals},this.props.children)}}let eA=Symbol.for("react.context"),eM=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===eA;if(-1===i.version.indexOf("19"))throw Error("react-konva version 19 is only compatible with React 19. Make sure to have the last version of react-konva and react or downgrade react-konva to version 18.");let eT=e=>{let t=i.useRef(null),r=i.useRef(null),n=i.useRef(null),a=function(e){let t=i.useRef({});return i.useLayoutEffect(()=>{t.current=e}),i.useLayoutEffect(()=>()=>{t.current={}},[]),t.current}(e),s=function(){let e=function(){let e=function(){let e=i.useContext(ek);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=i.useId();return i.useMemo(()=>{for(let r of[e,null==e?void 0:e.alternate]){if(!r)continue;let e=function e(t,r,n){if(!t)return;if(!0===n(t))return t;let i=r?t.return:t.child;for(;i;){let t=e(i,r,n);if(t)return t;i=r?null:i.sibling}}(r,!1,e=>{let r=e.memoizedState;for(;r;){if(r.memoizedState===t)return!0;r=r.next}});if(e)return e}},[e,t])}(),[t]=i.useState(()=>new Map);t.clear();let r=e;for(;r;){let e=r.type;eM(e)&&e!==ek&&!t.has(e)&&t.set(e,i.use(eC(e))),r=r.return}return t}();return i.useMemo(()=>Array.from(e.keys()).reduce((t,r)=>n=>i.createElement(t,null,i.createElement(r.Provider,{...n,value:e.get(r)})),e=>i.createElement(eP,{...e})),[e])}(),l=i.useRef(!1),h=t=>{let{forwardedRef:r}=e;r&&("function"==typeof r?r(t):r.current=t)},c=(()=>{let e=i.useRef(0);return i.useMemo(()=>{e.current++},[]),e.current>1})();return i.useLayoutEffect(()=>l.current&&c?()=>{l.current=!1,h(null),eO.updateContainer(null,n.current,null),r.current.destroy()}:(l.current=!0,r.current=new(o()).Stage({width:e.width,height:e.height,container:t.current}),h(r.current),n.current=eO.createContainer(r.current,d.ConcurrentRoot,null,!1,null,"",console.error,console.error,console.error,null),eO.updateContainer(i.createElement(s,{},e.children),n.current,null,()=>{}),()=>{c||(h(null),eO.updateContainer(null,n.current,null),r.current.destroy())}),[]),i.useLayoutEffect(()=>{h(r.current),y(r.current,e,a),eO.updateContainer(i.createElement(s,{},e.children),n.current,null)}),i.createElement("div",{ref:t,id:e.id,accessKey:e.accessKey,className:e.className,role:e.role,style:e.style,tabIndex:e.tabIndex,title:e.title})},eE="Layer",eR="Rect",eD="Image",eN="Text",eF="Transformer",eO=l()(n);eO.injectIntoDevTools({findHostInstanceByFiber:()=>null,bundleType:0,version:i.version,rendererPackageName:"react-konva"});let eL=i.forwardRef((e,t)=>i.createElement(eP,{},i.createElement(eT,{...e,forwardedRef:t})))},7728:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FastLayer=void 0;let n=r(6509),i=r(7498),a=r(6878);class o extends i.Layer{constructor(e){super(e),this.listening(!1),n.Util.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}}t.FastLayer=o,o.prototype.nodeType="FastLayer",(0,a._registerNode)(o)},7733:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},7850:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("flip-horizontal",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3",key:"1i73f7"}],["path",{d:"M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3",key:"saxlbk"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 2v2",key:"tus03m"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,r)=>{r.d(t,{n:()=>c});var n=r(2115),i=r(6101),a=r(3655),o=r(9033),s=r(5155),l="focusScope.autoFocusOnMount",d="focusScope.autoFocusOnUnmount",h={bubbles:!1,cancelable:!0},c=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:c=!1,onMountAutoFocus:m,onUnmountAutoFocus:v,...y}=e,[b,x]=n.useState(null),w=(0,o.c)(m),_=(0,o.c)(v),S=n.useRef(null),C=(0,i.s)(t,e=>x(e)),k=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(c){let e=function(e){if(k.paused||!b)return;let t=e.target;b.contains(t)?S.current=t:p(S.current,{select:!0})},t=function(e){if(k.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||p(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[c,b,k.paused]),n.useEffect(()=>{if(b){g.add(k);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,h);b.addEventListener(l,w),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(p(n,{select:t}),document.activeElement!==r)return}(u(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(b))}return()=>{b.removeEventListener(l,w),setTimeout(()=>{let t=new CustomEvent(d,h);b.addEventListener(d,_),b.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),b.removeEventListener(d,_),g.remove(k)},0)}}},[b,w,_,k]);let P=n.useCallback(e=>{if(!r&&!c||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[i,a]=function(e){let t=u(e);return[f(t,e),f(t.reverse(),e)]}(t);i&&a?e.shiftKey||n!==a?e.shiftKey&&n===i&&(e.preventDefault(),r&&p(a,{select:!0})):(e.preventDefault(),r&&p(i,{select:!0})):n===t&&e.preventDefault()}},[r,c,k.paused]);return(0,s.jsx)(a.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:P})});function u(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function f(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}c.displayName="FocusScope";var g=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=m(e,t)).unshift(t)},remove(t){var r;null==(r=(e=m(e,t))[0])||r.resume()}}}();function m(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},7924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8116:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HSL=void 0;let n=r(8949),i=r(913),a=r(6394);n.Factory.addGetterSetter(i.Node,"hue",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"saturation",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"luminance",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter),t.HSL=function(e){let t,r,n,i,a=e.data,o=a.length,s=Math.pow(2,this.saturation()),l=Math.abs(this.hue()+360)%360,d=127*this.luminance(),h=s*Math.cos(l*Math.PI/180),c=s*Math.sin(l*Math.PI/180),u=.299+.701*h+.167*c,f=.587-.587*h+.33*c,p=.114-.114*h-.497*c,g=.299-.299*h-.328*c,m=.587+.413*h+.035*c,v=.114-.114*h+.293*c,y=.299-.3*h+1.25*c,b=.587-.586*h-1.05*c,x=.114+.886*h-.2*c;for(let e=0;e<o;e+=4)t=a[e+0],r=a[e+1],n=a[e+2],i=a[e+3],a[e+0]=u*t+f*r+p*n+d,a[e+1]=g*t+m*r+v*n+d,a[e+2]=y*t+b*r+x*n+d,a[e+3]=i}},8164:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8168:(e,t,r)=>{r.d(t,{Eq:()=>d});var n=new WeakMap,i=new WeakMap,a={},o=0,s=function(e){return e&&(e.host||s(e.parentNode))},l=function(e,t,r,l){var d=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var h=a[r],c=[],u=new Set,f=new Set(d),p=function(e){!e||u.has(e)||(u.add(e),p(e.parentNode))};d.forEach(p);var g=function(e){!e||f.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))g(e);else try{var t=e.getAttribute(l),a=null!==t&&"false"!==t,o=(n.get(e)||0)+1,s=(h.get(e)||0)+1;n.set(e,o),h.set(e,s),c.push(e),1===o&&a&&i.set(e,!0),1===s&&e.setAttribute(r,"true"),a||e.setAttribute(l,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return g(t),u.clear(),o++,function(){c.forEach(function(e){var t=n.get(e)-1,a=h.get(e)-1;n.set(e,t),h.set(e,a),t||(i.has(e)||e.removeAttribute(l),i.delete(e)),a||e.removeAttribute(r)}),--o||(n=new WeakMap,n=new WeakMap,i=new WeakMap,a={})}},d=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),i=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return i?(n.push.apply(n,Array.from(i.querySelectorAll("[aria-live], script"))),l(n,i,r,"aria-hidden")):function(){return null}}},8197:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Invert=void 0,t.Invert=function(e){let t=e.data,r=t.length;for(let e=0;e<r;e+=4)t[e]=255-t[e],t[e+1]=255-t[e+1],t[e+2]=255-t[e+2]}},8650:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Line=void 0;let n=r(8949),i=r(6878),a=r(3306),o=r(6394);function s(e,t,r,n,i,a,o){let s=Math.sqrt(Math.pow(r-e,2)+Math.pow(n-t,2)),l=Math.sqrt(Math.pow(i-r,2)+Math.pow(a-n,2)),d=o*s/(s+l),h=o*l/(s+l);return[r-d*(i-e),n-d*(a-t),r+h*(i-e),n+h*(a-t)]}function l(e,t){let r=e.length,n=[];for(let i=2;i<r-2;i+=2){let r=s(e[i-2],e[i-1],e[i],e[i+1],e[i+2],e[i+3],t);isNaN(r[0])||(n.push(r[0]),n.push(r[1]),n.push(e[i]),n.push(e[i+1]),n.push(r[2]),n.push(r[3]))}return n}class d extends a.Shape{constructor(e){super(e),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",function(){this._clearCache("tensionPoints")})}_sceneFunc(e){let t=this.points(),r=t.length,n=this.tension(),i=this.closed(),a=this.bezier();if(!r)return;let o=0;if(e.beginPath(),e.moveTo(t[0],t[1]),0!==n&&r>4){let n=this.getTensionPoints(),a=n.length;for(o=4*!i,i||e.quadraticCurveTo(n[0],n[1],n[2],n[3]);o<a-2;)e.bezierCurveTo(n[o++],n[o++],n[o++],n[o++],n[o++],n[o++]);i||e.quadraticCurveTo(n[a-2],n[a-1],t[r-2],t[r-1])}else if(a)for(o=2;o<r;)e.bezierCurveTo(t[o++],t[o++],t[o++],t[o++],t[o++],t[o++]);else for(o=2;o<r;o+=2)e.lineTo(t[o],t[o+1]);i?(e.closePath(),e.fillStrokeShape(this)):e.strokeShape(this)}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():l(this.points(),this.tension())}_getTensionPointsClosed(){let e=this.points(),t=e.length,r=this.tension(),n=s(e[t-2],e[t-1],e[0],e[1],e[2],e[3],r),i=s(e[t-4],e[t-3],e[t-2],e[t-1],e[0],e[1],r),a=l(e,r);return[n[2],n[3]].concat(a).concat([i[0],i[1],e[t-2],e[t-1],i[2],i[3],n[0],n[1],e[0],e[1]])}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){let e,t,r=this.points();if(r.length<4)return{x:r[0]||0,y:r[1]||0,width:0,height:0};let n=(r=0!==this.tension()?[r[0],r[1],...this._getTensionPoints(),r[r.length-2],r[r.length-1]]:this.points())[0],i=r[0],a=r[1],o=r[1];for(let s=0;s<r.length/2;s++)e=r[2*s],t=r[2*s+1],n=Math.min(n,e),i=Math.max(i,e),a=Math.min(a,t),o=Math.max(o,t);return{x:n,y:a,width:i-n,height:o-a}}}t.Line=d,d.prototype.className="Line",d.prototype._attrsAffectingSize=["points","bezier","tension"],(0,i._registerNode)(d),n.Factory.addGetterSetter(d,"closed",!1),n.Factory.addGetterSetter(d,"bezier",!1),n.Factory.addGetterSetter(d,"tension",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(d,"points",[],(0,o.getNumberArrayValidator)())},8698:(e,t,r)=>{r.d(t,{UC:()=>e0,YJ:()=>e1,q7:()=>e5,JU:()=>e2,ZL:()=>eJ,z6:()=>e3,bL:()=>eZ,wv:()=>e4,Pb:()=>e6,G5:()=>e8,ZP:()=>e9,l9:()=>eQ});var n=r(2115),i=r(5185),a=r(6101),o=r(6081),s=r(5845),l=r(3655),d=r(7328),h=r(4315),c=r(9178),u=r(2293),f=r(7900),p=r(1285),g=r(5152),m=r(4378),v=r(8905),y=r(9196),b=r(9708),x=r(9033),w=r(8168),_=r(3795),S=r(5155),C=["Enter"," "],k=["ArrowUp","PageDown","End"],P=["ArrowDown","PageUp","Home",...k],A={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},M={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[E,R,D]=(0,d.N)(T),[N,F]=(0,o.A)(T,[D,g.Bk,y.RG]),O=(0,g.Bk)(),L=(0,y.RG)(),[I,G]=N(T),[j,U]=N(T),B=e=>{let{__scopeMenu:t,open:r=!1,children:i,dir:a,onOpenChange:o,modal:s=!0}=e,l=O(t),[d,c]=n.useState(null),u=n.useRef(!1),f=(0,x.c)(o),p=(0,h.jH)(a);return n.useEffect(()=>{let e=()=>{u.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>u.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,S.jsx)(g.bL,{...l,children:(0,S.jsx)(I,{scope:t,open:r,onOpenChange:f,content:d,onContentChange:c,children:(0,S.jsx)(j,{scope:t,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:u,dir:p,modal:s,children:i})})})};B.displayName=T;var z=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=O(r);return(0,S.jsx)(g.Mz,{...i,...n,ref:t})});z.displayName="MenuAnchor";var H="MenuPortal",[V,W]=N(H,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:i}=e,a=G(H,t);return(0,S.jsx)(V,{scope:t,forceMount:r,children:(0,S.jsx)(v.C,{present:r||a.open,children:(0,S.jsx)(m.Z,{asChild:!0,container:i,children:n})})})};K.displayName=H;var Y="MenuContent",[q,X]=N(Y),$=n.forwardRef((e,t)=>{let r=W(Y,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,a=G(Y,e.__scopeMenu),o=U(Y,e.__scopeMenu);return(0,S.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:n||a.open,children:(0,S.jsx)(E.Slot,{scope:e.__scopeMenu,children:o.modal?(0,S.jsx)(Z,{...i,ref:t}):(0,S.jsx)(Q,{...i,ref:t})})})})}),Z=n.forwardRef((e,t)=>{let r=G(Y,e.__scopeMenu),o=n.useRef(null),s=(0,a.s)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,w.Eq)(e)},[]),(0,S.jsx)(ee,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=G(Y,e.__scopeMenu);return(0,S.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,b.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:d,disableOutsidePointerEvents:h,onEntryFocus:p,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:x,onDismiss:w,disableOutsideScroll:C,...A}=e,M=G(Y,r),T=U(Y,r),E=O(r),D=L(r),N=R(r),[F,I]=n.useState(null),j=n.useRef(null),B=(0,a.s)(t,j,M.onContentChange),z=n.useRef(0),H=n.useRef(""),V=n.useRef(0),W=n.useRef(null),K=n.useRef("right"),X=n.useRef(0),$=C?_.A:n.Fragment;n.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,u.Oh)();let Z=n.useCallback(e=>{var t,r;return K.current===(null==(t=W.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let o=t[e],s=t[a],l=o.x,d=o.y,h=s.x,c=s.y;d>n!=c>n&&r<(h-l)*(n-d)/(c-d)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,null==(r=W.current)?void 0:r.area)},[]);return(0,S.jsx)(q,{scope:r,searchRef:H,onItemEnter:n.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),onItemLeave:n.useCallback(e=>{var t;Z(e)||(null==(t=j.current)||t.focus(),I(null))},[Z]),onTriggerLeave:n.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),pointerGraceTimerRef:V,onPointerGraceIntentChange:n.useCallback(e=>{W.current=e},[]),children:(0,S.jsx)($,{...C?{as:J,allowPinchZoom:!0}:void 0,children:(0,S.jsx)(f.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,i.m)(l,e=>{var t;e.preventDefault(),null==(t=j.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,S.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:h,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:x,onDismiss:w,children:(0,S.jsx)(y.bL,{asChild:!0,...D,dir:T.dir,orientation:"vertical",loop:o,currentTabStopId:F,onCurrentTabStopIdChange:I,onEntryFocus:(0,i.m)(p,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,S.jsx)(g.UC,{role:"menu","aria-orientation":"vertical","data-state":eM(M.open),"data-radix-menu-content":"",dir:T.dir,...E,...A,ref:B,style:{outline:"none",...A.style},onKeyDown:(0,i.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&(e=>{var t,r;let n=H.current+e,i=N().filter(e=>!e.disabled),a=document.activeElement,o=null==(t=i.find(e=>e.ref.current===a))?void 0:t.textValue,s=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,o=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(o=o.filter(e=>e!==r));let s=o.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}(i.map(e=>e.textValue),n,o),l=null==(r=i.find(e=>e.textValue===s))?void 0:r.ref.current;!function e(t){H.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(n),l&&setTimeout(()=>l.focus())})(e.key));let i=j.current;if(e.target!==i||!P.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);k.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),H.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eR(e=>{let t=e.target,r=X.current!==e.clientX;e.currentTarget.contains(t)&&r&&(K.current=e.clientX>X.current?"right":"left",X.current=e.clientX)}))})})})})})})});$.displayName=Y;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,S.jsx)(l.sG.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,S.jsx)(l.sG.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",ei="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...s}=e,d=n.useRef(null),h=U(en,e.__scopeMenu),c=X(en,e.__scopeMenu),u=(0,a.s)(t,d),f=n.useRef(!1);return(0,S.jsx)(eo,{...s,ref:u,disabled:r,onClick:(0,i.m)(e.onClick,()=>{let e=d.current;if(!r&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>null==o?void 0:o(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?f.current=!1:h.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),f.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;r||t&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var eo=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:s,...d}=e,h=X(en,r),c=L(r),u=n.useRef(null),f=(0,a.s)(t,u),[p,g]=n.useState(!1),[m,v]=n.useState("");return n.useEffect(()=>{let e=u.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[d.children]),(0,S.jsx)(E.ItemSlot,{scope:r,disabled:o,textValue:null!=s?s:m,children:(0,S.jsx)(y.q7,{asChild:!0,...c,focusable:!o,children:(0,S.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...d,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,eR(e=>{o?h.onItemLeave(e):(h.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eR(e=>h.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>g(!0)),onBlur:(0,i.m)(e.onBlur,()=>g(!1))})})})}),es=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,S.jsx)(eg,{scope:e.__scopeMenu,checked:r,children:(0,S.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eT(r)?"mixed":r,...a,ref:t,"data-state":eE(r),onSelect:(0,i.m)(a.onSelect,()=>null==n?void 0:n(!!eT(r)||!r),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ed,eh]=N(el,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,a=(0,x.c)(n);return(0,S.jsx)(ed,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,S.jsx)(et,{...i,ref:t})})});ec.displayName=el;var eu="MenuRadioItem",ef=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eh(eu,e.__scopeMenu),o=r===a.value;return(0,S.jsx)(eg,{scope:e.__scopeMenu,checked:o,children:(0,S.jsx)(ea,{role:"menuitemradio","aria-checked":o,...n,ref:t,"data-state":eE(o),onSelect:(0,i.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ef.displayName=eu;var ep="MenuItemIndicator",[eg,em]=N(ep,{checked:!1}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,a=em(ep,r);return(0,S.jsx)(v.C,{present:n||eT(a.checked)||!0===a.checked,children:(0,S.jsx)(l.sG.span,{...i,ref:t,"data-state":eE(a.checked)})})});ev.displayName=ep;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,S.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ey.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=O(r);return(0,S.jsx)(g.i3,{...i,...n,ref:t})});eb.displayName="MenuArrow";var ex="MenuSub",[ew,e_]=N(ex),eS=e=>{let{__scopeMenu:t,children:r,open:i=!1,onOpenChange:a}=e,o=G(ex,t),s=O(t),[l,d]=n.useState(null),[h,c]=n.useState(null),u=(0,x.c)(a);return n.useEffect(()=>(!1===o.open&&u(!1),()=>u(!1)),[o.open,u]),(0,S.jsx)(g.bL,{...s,children:(0,S.jsx)(I,{scope:t,open:i,onOpenChange:u,content:h,onContentChange:c,children:(0,S.jsx)(ew,{scope:t,contentId:(0,p.B)(),triggerId:(0,p.B)(),trigger:l,onTriggerChange:d,children:r})})})};eS.displayName=ex;var eC="MenuSubTrigger",ek=n.forwardRef((e,t)=>{let r=G(eC,e.__scopeMenu),o=U(eC,e.__scopeMenu),s=e_(eC,e.__scopeMenu),l=X(eC,e.__scopeMenu),d=n.useRef(null),{pointerGraceTimerRef:h,onPointerGraceIntentChange:c}=l,u={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=h.current;return()=>{window.clearTimeout(e),c(null)}},[h,c]),(0,S.jsx)(z,{asChild:!0,...u,children:(0,S.jsx)(eo,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":eM(r.open),...e,ref:(0,a.t)(t,s.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eR(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||d.current||(l.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eR(e=>{var t,n;f();let i=null==(t=r.content)?void 0:t.getBoundingClientRect();if(i){let t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,o=i[a?"left":"right"],s=i[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:i.top},{x:s,y:i.top},{x:s,y:i.bottom},{x:o,y:i.bottom}],side:t}),window.clearTimeout(h.current),h.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&A[o.dir].includes(t.key)){var i;r.onOpenChange(!0),null==(i=r.content)||i.focus(),t.preventDefault()}})})})});ek.displayName=eC;var eP="MenuSubContent",eA=n.forwardRef((e,t)=>{let r=W(Y,e.__scopeMenu),{forceMount:o=r.forceMount,...s}=e,l=G(Y,e.__scopeMenu),d=U(Y,e.__scopeMenu),h=e_(eP,e.__scopeMenu),c=n.useRef(null),u=(0,a.s)(t,c);return(0,S.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(v.C,{present:o||l.open,children:(0,S.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,S.jsx)(ee,{id:h.contentId,"aria-labelledby":h.triggerId,...s,ref:u,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;d.isUsingKeyboardRef.current&&(null==(t=c.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==h.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=M[d.dir].includes(e.key);if(t&&r){var n;l.onOpenChange(!1),null==(n=h.trigger)||n.focus(),e.preventDefault()}})})})})})});function eM(e){return e?"open":"closed"}function eT(e){return"indeterminate"===e}function eE(e){return eT(e)?"indeterminate":e?"checked":"unchecked"}function eR(e){return t=>"mouse"===t.pointerType?e(t):void 0}eA.displayName=eP;var eD="DropdownMenu",[eN,eF]=(0,o.A)(eD,[F]),eO=F(),[eL,eI]=eN(eD),eG=e=>{let{__scopeDropdownMenu:t,children:r,dir:i,open:a,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,h=eO(t),c=n.useRef(null),[u,f]=(0,s.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:eD});return(0,S.jsx)(eL,{scope:t,triggerId:(0,p.B)(),triggerRef:c,contentId:(0,p.B)(),open:u,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,S.jsx)(B,{...h,open:u,onOpenChange:f,dir:i,modal:d,children:r})})};eG.displayName=eD;var ej="DropdownMenuTrigger",eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,s=eI(ej,r),d=eO(r);return(0,S.jsx)(z,{asChild:!0,...d,children:(0,S.jsx)(l.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,a.t)(t,s.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=ej;var eB=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eO(t);return(0,S.jsx)(K,{...n,...r})};eB.displayName="DropdownMenuPortal";var ez="DropdownMenuContent",eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,o=eI(ez,r),s=eO(r),l=n.useRef(!1);return(0,S.jsx)($,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...a,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=o.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eH.displayName=ez;var eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(et,{...i,...n,ref:t})});eV.displayName="DropdownMenuGroup";var eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(er,{...i,...n,ref:t})});eW.displayName="DropdownMenuLabel";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ea,{...i,...n,ref:t})});eK.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(es,{...i,...n,ref:t})}).displayName="DropdownMenuCheckboxItem";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ec,{...i,...n,ref:t})});eY.displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ef,{...i,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ev,{...i,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ey,{...i,...n,ref:t})});eq.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(eb,{...i,...n,ref:t})}).displayName="DropdownMenuArrow";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(ek,{...i,...n,ref:t})});eX.displayName="DropdownMenuSubTrigger";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eO(r);return(0,S.jsx)(eA,{...i,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var eZ=eG,eQ=eU,eJ=eB,e0=eH,e1=eV,e2=eW,e5=eK,e3=eY,e4=eq,e6=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:i,defaultOpen:a}=e,o=eO(t),[l,d]=(0,s.i)({prop:n,defaultProp:null!=a&&a,onChange:i,caller:"DropdownMenuSub"});return(0,S.jsx)(eS,{...o,open:l,onOpenChange:d,children:r})},e9=eX,e8=e$},8702:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},8749:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8770:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Brighten=void 0;let n=r(8949),i=r(913),a=r(6394);t.Brighten=function(e){let t=255*this.brightness(),r=e.data,n=r.length;for(let e=0;e<n;e+=4)r[e]+=t,r[e+1]+=t,r[e+2]+=t},n.Factory.addGetterSetter(i.Node,"brightness",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},8859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},8905:(e,t,r)=>{r.d(t,{C:()=>o});var n=r(2115),i=r(6101),a=r(2712),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[i,o]=n.useState(),l=n.useRef(null),d=n.useRef(e),h=n.useRef("none"),[c,u]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=s(l.current);h.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=l.current,r=d.current;if(r!==e){let n=h.current,i=s(t);e?u("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?u("UNMOUNT"):r&&n!==i?u("ANIMATION_OUT"):u("UNMOUNT"),d.current=e}},[e,u]),(0,a.N)(()=>{if(i){var e;let t,r=null!=(e=i.ownerDocument.defaultView)?e:window,n=e=>{let n=s(l.current).includes(e.animationName);if(e.target===i&&n&&(u("ANIMATION_END"),!d.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},a=e=>{e.target===i&&(h.current=s(l.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}u("ANIMATION_END")},[i,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),d=(0,i.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:d}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},8932:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},8949:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Factory=void 0;let n=r(6509),i=r(6394);t.Factory={addGetterSetter(e,r,n,i,a){t.Factory.addGetter(e,r,n),t.Factory.addSetter(e,r,i,a),t.Factory.addOverloadedGetterSetter(e,r)},addGetter(e,t,r){let i="get"+n.Util._capitalize(t);e.prototype[i]=e.prototype[i]||function(){let e=this.attrs[t];return void 0===e?r:e}},addSetter(e,r,i,a){let o="set"+n.Util._capitalize(r);e.prototype[o]||t.Factory.overWriteSetter(e,r,i,a)},overWriteSetter(e,t,r,i){let a="set"+n.Util._capitalize(t);e.prototype[a]=function(e){return r&&null!=e&&(e=r.call(this,e,t)),this._setAttr(t,e),i&&i.call(this),this}},addComponentsGetterSetter(e,r,a,o,s){let l=a.length,d=n.Util._capitalize,h="get"+d(r),c="set"+d(r);e.prototype[h]=function(){let e={};for(let t=0;t<l;t++){let n=a[t];e[n]=this.getAttr(r+d(n))}return e};let u=(0,i.getComponentValidator)(a);e.prototype[c]=function(e){let t=this.attrs[r];for(let t in o&&(e=o.call(this,e,r)),u&&u.call(this,e,r),e)e.hasOwnProperty(t)&&this._setAttr(r+d(t),e[t]);return e||a.forEach(e=>{this._setAttr(r+d(e),void 0)}),this._fireChangeEvent(r,t,e),s&&s.call(this),this},t.Factory.addOverloadedGetterSetter(e,r)},addOverloadedGetterSetter(e,t){let r=n.Util._capitalize(t),i="set"+r,a="get"+r;e.prototype[t]=function(){return arguments.length?(this[i](arguments[0]),this):this[a]()}},addDeprecatedGetterSetter(e,r,i,a){n.Util.error("Adding deprecated "+r);let o="get"+n.Util._capitalize(r),s=r+" property is deprecated and will be removed soon. Look at Konva change log for more information.";e.prototype[o]=function(){n.Util.error(s);let e=this.attrs[r];return void 0===e?i:e},t.Factory.addSetter(e,r,a,function(){n.Util.error(s)}),t.Factory.addOverloadedGetterSetter(e,r)},backCompat(e,t){n.Util.each(t,function(t,r){let i=e.prototype[r],a="get"+n.Util._capitalize(t),o="set"+n.Util._capitalize(t);function s(){i.apply(this,arguments),n.Util.error('"'+t+'" method is deprecated and will be removed soon. Use ""'+r+'" instead.')}e.prototype[t]=s,e.prototype[a]=s,e.prototype[o]=s})},afterSetFilter(){this._filterUpToDate=!1}}},9012:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HitContext=t.SceneContext=t.Context=void 0;let n=r(6509),i=r(6878),a=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","roundRect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"];class o{constructor(e){this.canvas=e,i.Konva.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(e){e.fillEnabled()&&this._fill(e)}_fill(e){}strokeShape(e){e.hasStroke()&&this._stroke(e)}_stroke(e){}fillStrokeShape(e){e.attrs.fillAfterStrokeEnabled?(this.strokeShape(e),this.fillShape(e)):(this.fillShape(e),this.strokeShape(e))}getTrace(e,t){let r=this.traceArr,i=r.length,a="",o,s,l,d;for(o=0;o<i;o++)(l=(s=r[o]).method)?(d=s.args,a+=l,e?a+="()":n.Util._isArray(d[0])?a+="(["+d.join(",")+"])":(t&&(d=d.map(e=>"number"==typeof e?Math.floor(e):e)),a+="("+d.join(",")+")")):(a+=s.property,e||(a+="="+s.val)),a+=";";return a}clearTrace(){this.traceArr=[]}_trace(e){let t=this.traceArr;t.push(e),t.length>=100&&t.shift()}reset(){let e=this.getCanvas().getPixelRatio();this.setTransform(+e,0,0,+e,0,0)}getCanvas(){return this.canvas}clear(e){let t=this.getCanvas();e?this.clearRect(e.x||0,e.y||0,e.width||0,e.height||0):this.clearRect(0,0,t.getWidth()/t.pixelRatio,t.getHeight()/t.pixelRatio)}_applyLineCap(e){let t=e.attrs.lineCap;t&&this.setAttr("lineCap",t)}_applyOpacity(e){let t=e.getAbsoluteOpacity();1!==t&&this.setAttr("globalAlpha",t)}_applyLineJoin(e){let t=e.attrs.lineJoin;t&&this.setAttr("lineJoin",t)}setAttr(e,t){this._context[e]=t}arc(e,t,r,n,i,a){this._context.arc(e,t,r,n,i,a)}arcTo(e,t,r,n,i){this._context.arcTo(e,t,r,n,i)}beginPath(){this._context.beginPath()}bezierCurveTo(e,t,r,n,i,a){this._context.bezierCurveTo(e,t,r,n,i,a)}clearRect(e,t,r,n){this._context.clearRect(e,t,r,n)}clip(...e){this._context.clip.apply(this._context,e)}closePath(){this._context.closePath()}createImageData(e,t){let r=arguments;return 2===r.length?this._context.createImageData(e,t):1===r.length?this._context.createImageData(e):void 0}createLinearGradient(e,t,r,n){return this._context.createLinearGradient(e,t,r,n)}createPattern(e,t){return this._context.createPattern(e,t)}createRadialGradient(e,t,r,n,i,a){return this._context.createRadialGradient(e,t,r,n,i,a)}drawImage(e,t,r,n,i,a,o,s,l){let d=arguments,h=this._context;3===d.length?h.drawImage(e,t,r):5===d.length?h.drawImage(e,t,r,n,i):9===d.length&&h.drawImage(e,t,r,n,i,a,o,s,l)}ellipse(e,t,r,n,i,a,o,s){this._context.ellipse(e,t,r,n,i,a,o,s)}isPointInPath(e,t,r,n){return r?this._context.isPointInPath(r,e,t,n):this._context.isPointInPath(e,t,n)}fill(...e){this._context.fill.apply(this._context,e)}fillRect(e,t,r,n){this._context.fillRect(e,t,r,n)}strokeRect(e,t,r,n){this._context.strokeRect(e,t,r,n)}fillText(e,t,r,n){n?this._context.fillText(e,t,r,n):this._context.fillText(e,t,r)}measureText(e){return this._context.measureText(e)}getImageData(e,t,r,n){return this._context.getImageData(e,t,r,n)}lineTo(e,t){this._context.lineTo(e,t)}moveTo(e,t){this._context.moveTo(e,t)}rect(e,t,r,n){this._context.rect(e,t,r,n)}roundRect(e,t,r,n,i){this._context.roundRect(e,t,r,n,i)}putImageData(e,t,r){this._context.putImageData(e,t,r)}quadraticCurveTo(e,t,r,n){this._context.quadraticCurveTo(e,t,r,n)}restore(){this._context.restore()}rotate(e){this._context.rotate(e)}save(){this._context.save()}scale(e,t){this._context.scale(e,t)}setLineDash(e){this._context.setLineDash?this._context.setLineDash(e):"mozDash"in this._context?this._context.mozDash=e:"webkitLineDash"in this._context&&(this._context.webkitLineDash=e)}getLineDash(){return this._context.getLineDash()}setTransform(e,t,r,n,i,a){this._context.setTransform(e,t,r,n,i,a)}stroke(e){e?this._context.stroke(e):this._context.stroke()}strokeText(e,t,r,n){this._context.strokeText(e,t,r,n)}transform(e,t,r,n,i,a){this._context.transform(e,t,r,n,i,a)}translate(e,t){this._context.translate(e,t)}_enableTrace(){let e=this,t=a.length,r=this.setAttr,i,o,s=function(t){let r=e[t],i;e[t]=function(){return o=function(e){let t=[],r=e.length,i=n.Util;for(let n=0;n<r;n++){let r=e[n];i._isNumber(r)?r=Math.round(1e3*r)/1e3:i._isString(r)||(r+=""),t.push(r)}return t}(Array.prototype.slice.call(arguments,0)),i=r.apply(e,arguments),e._trace({method:t,args:o}),i}};for(i=0;i<t;i++)s(a[i]);e.setAttr=function(){r.apply(e,arguments);let t=arguments[0],n=arguments[1];("shadowOffsetX"===t||"shadowOffsetY"===t||"shadowBlur"===t)&&(n/=this.canvas.getPixelRatio()),e._trace({property:t,val:n})}}_applyGlobalCompositeOperation(e){let t=e.attrs.globalCompositeOperation;t&&"source-over"!==t&&this.setAttr("globalCompositeOperation",t)}}t.Context=o,["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","letterSpacing","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","direction","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach(function(e){Object.defineProperty(o.prototype,e,{get(){return this._context[e]},set(t){this._context[e]=t}})});class s extends o{constructor(e,{willReadFrequently:t=!1}={}){super(e),this._context=e._canvas.getContext("2d",{willReadFrequently:t})}_fillColor(e){let t=e.fill();this.setAttr("fillStyle",t),e._fillFunc(this)}_fillPattern(e){this.setAttr("fillStyle",e._getFillPattern()),e._fillFunc(this)}_fillLinearGradient(e){let t=e._getLinearGradient();t&&(this.setAttr("fillStyle",t),e._fillFunc(this))}_fillRadialGradient(e){let t=e._getRadialGradient();t&&(this.setAttr("fillStyle",t),e._fillFunc(this))}_fill(e){let t=e.fill(),r=e.getFillPriority();if(t&&"color"===r)return void this._fillColor(e);let n=e.getFillPatternImage();if(n&&"pattern"===r)return void this._fillPattern(e);let i=e.getFillLinearGradientColorStops();if(i&&"linear-gradient"===r)return void this._fillLinearGradient(e);let a=e.getFillRadialGradientColorStops();if(a&&"radial-gradient"===r)return void this._fillRadialGradient(e);t?this._fillColor(e):n?this._fillPattern(e):i?this._fillLinearGradient(e):a&&this._fillRadialGradient(e)}_strokeLinearGradient(e){let t=e.getStrokeLinearGradientStartPoint(),r=e.getStrokeLinearGradientEndPoint(),n=e.getStrokeLinearGradientColorStops(),i=this.createLinearGradient(t.x,t.y,r.x,r.y);if(n){for(let e=0;e<n.length;e+=2)i.addColorStop(n[e],n[e+1]);this.setAttr("strokeStyle",i)}}_stroke(e){let t=e.dash(),r=e.getStrokeScaleEnabled();if(e.hasStroke()){if(!r){this.save();let e=this.getCanvas().getPixelRatio();this.setTransform(e,0,0,e,0,0)}this._applyLineCap(e),t&&e.dashEnabled()&&(this.setLineDash(t),this.setAttr("lineDashOffset",e.dashOffset())),this.setAttr("lineWidth",e.strokeWidth()),e.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)"),e.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(e):this.setAttr("strokeStyle",e.stroke()),e._strokeFunc(this),r||this.restore()}}_applyShadow(e){var t,r,n;let i=null!=(t=e.getShadowRGBA())?t:"black",a=null!=(r=e.getShadowBlur())?r:5,o=null!=(n=e.getShadowOffset())?n:{x:0,y:0},s=e.getAbsoluteScale(),l=this.canvas.getPixelRatio(),d=s.x*l,h=s.y*l;this.setAttr("shadowColor",i),this.setAttr("shadowBlur",a*Math.min(Math.abs(d),Math.abs(h))),this.setAttr("shadowOffsetX",o.x*d),this.setAttr("shadowOffsetY",o.y*h)}}t.SceneContext=s;class l extends o{constructor(e){super(e),this._context=e._canvas.getContext("2d",{willReadFrequently:!0})}_fill(e){this.save(),this.setAttr("fillStyle",e.colorKey),e._fillFuncHit(this),this.restore()}strokeShape(e){e.hasHitStroke()&&this._stroke(e)}_stroke(e){if(e.hasHitStroke()){let t=e.getStrokeScaleEnabled();if(!t){this.save();let e=this.getCanvas().getPixelRatio();this.setTransform(e,0,0,e,0,0)}this._applyLineCap(e);let r=e.hitStrokeWidth(),n="auto"===r?e.strokeWidth():r;this.setAttr("lineWidth",n),this.setAttr("strokeStyle",e.colorKey),e._strokeFuncHit(this),t||this.restore()}}}t.HitContext=l},9033:(e,t,r)=>{r.d(t,{c:()=>i});var n=r(2115);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},9044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Threshold=void 0;let n=r(8949),i=r(913),a=r(6394);t.Threshold=function(e){let t=255*this.threshold(),r=e.data,n=r.length;for(let e=0;e<n;e+=1)r[e]=r[e]<t?0:255},n.Factory.addGetterSetter(i.Node,"threshold",.5,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},9178:(e,t,r)=>{r.d(t,{qW:()=>u});var n,i=r(2115),a=r(5185),o=r(3655),s=r(6101),l=r(9033),d=r(5155),h="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),u=i.forwardRef((e,t)=>{var r,u;let{disableOutsidePointerEvents:g=!1,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:b,onDismiss:x,...w}=e,_=i.useContext(c),[S,C]=i.useState(null),k=null!=(u=null==S?void 0:S.ownerDocument)?u:null==(r=globalThis)?void 0:r.document,[,P]=i.useState({}),A=(0,s.s)(t,e=>C(e)),M=Array.from(_.layers),[T]=[..._.layersWithOutsidePointerEventsDisabled].slice(-1),E=M.indexOf(T),R=S?M.indexOf(S):-1,D=_.layersWithOutsidePointerEventsDisabled.size>0,N=R>=E,F=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,l.c)(e),a=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){p("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);a.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[..._.branches].some(e=>e.contains(t));N&&!r&&(null==v||v(e),null==b||b(e),e.defaultPrevented||null==x||x())},k),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,l.c)(e),a=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!a.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![..._.branches].some(e=>e.contains(t))&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==x||x())},k);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{R===_.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},k),i.useEffect(()=>{if(S)return g&&(0===_.layersWithOutsidePointerEventsDisabled.size&&(n=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),_.layersWithOutsidePointerEventsDisabled.add(S)),_.layers.add(S),f(),()=>{g&&1===_.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=n)}},[S,k,g,_]),i.useEffect(()=>()=>{S&&(_.layers.delete(S),_.layersWithOutsidePointerEventsDisabled.delete(S),f())},[S,_]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,d.jsx)(o.sG.div,{...w,ref:A,style:{pointerEvents:D?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,F.onPointerDownCapture)})});function f(){let e=new CustomEvent(h);document.dispatchEvent(e)}function p(e,t,r,n){let{discrete:i}=n,a=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),i?(0,o.hO)(a,s):a.dispatchEvent(s)}u.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(c),n=i.useRef(null),a=(0,s.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(o.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},9196:(e,t,r)=>{r.d(t,{RG:()=>w,bL:()=>E,q7:()=>R});var n=r(2115),i=r(5185),a=r(7328),o=r(6101),s=r(6081),l=r(1285),d=r(3655),h=r(9033),c=r(5845),u=r(4315),f=r(5155),p="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[v,y,b]=(0,a.N)(m),[x,w]=(0,s.A)(m,[b]),[_,S]=x(m),C=n.forwardRef((e,t)=>(0,f.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(k,{...e,ref:t})})}));C.displayName=m;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:S=!1,...C}=e,k=n.useRef(null),P=(0,o.s)(t,k),A=(0,u.jH)(l),[M,E]=(0,c.i)({prop:v,defaultProp:null!=b?b:null,onChange:x,caller:m}),[R,D]=n.useState(!1),N=(0,h.c)(w),F=y(r),O=n.useRef(!1),[L,I]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(p,N),()=>e.removeEventListener(p,N)},[N]),(0,f.jsx)(_,{scope:r,orientation:a,dir:A,loop:s,currentTabStopId:M,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:R||0===L?-1:0,"data-orientation":a,...C,ref:P,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(p,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),S)}}O.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>D(!1))})})}),P="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:o=!1,tabStopId:s,children:h,...c}=e,u=(0,l.B)(),p=s||u,g=S(P,r),m=g.currentTabStopId===p,b=y(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:_}=g;return n.useEffect(()=>{if(a)return x(),()=>w()},[a,x,w]),(0,f.jsx)(v.ItemSlot,{scope:r,id:p,focusable:a,active:o,children:(0,f.jsx)(d.sG.span,{tabIndex:m?0:-1,"data-orientation":g.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{a?g.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>g.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void g.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return M[i]}(e,g.orientation,g.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=g.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>T(r))}}),children:"function"==typeof h?h({isCurrentTabStop:m,hasTabStop:null!=_}):h})})});A.displayName=P;var M={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=C,R=A},9340:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Arc=void 0;let n=r(8949),i=r(3306),a=r(6878),o=r(6394),s=r(6878);class l extends i.Shape{_sceneFunc(e){let t=a.Konva.getAngle(this.angle()),r=this.clockwise();e.beginPath(),e.arc(0,0,this.outerRadius(),0,t,r),e.arc(0,0,this.innerRadius(),t,0,!r),e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(e){this.outerRadius(e/2)}setHeight(e){this.outerRadius(e/2)}getSelfRect(){let e=this.innerRadius(),t=this.outerRadius(),r=this.clockwise(),n=a.Konva.getAngle(r?360-this.angle():this.angle()),i=Math.cos(Math.min(n,Math.PI)),o=Math.sin(Math.min(Math.max(Math.PI,n),3*Math.PI/2)),s=Math.sin(Math.min(n,Math.PI/2)),l=i*(i>0?e:t),d=o*(o>0?e:t),h=s*(s>0?t:e);return{x:l,y:r?-1*h:d,width:t-l,height:h-d}}}t.Arc=l,l.prototype._centroid=!0,l.prototype.className="Arc",l.prototype._attrsAffectingSize=["innerRadius","outerRadius","angle","clockwise"],(0,s._registerNode)(l),n.Factory.addGetterSetter(l,"innerRadius",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"outerRadius",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"angle",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"clockwise",!1,(0,o.getBooleanValidator)())},9367:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},9408:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Wedge=void 0;let n=r(8949),i=r(3306),a=r(6878),o=r(6394),s=r(6878);class l extends i.Shape{_sceneFunc(e){e.beginPath(),e.arc(0,0,this.radius(),0,a.Konva.getAngle(this.angle()),this.clockwise()),e.lineTo(0,0),e.closePath(),e.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(e){this.radius(e/2)}setHeight(e){this.radius(e/2)}}t.Wedge=l,l.prototype.className="Wedge",l.prototype._centroid=!0,l.prototype._attrsAffectingSize=["radius"],(0,s._registerNode)(l),n.Factory.addGetterSetter(l,"radius",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"angle",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(l,"clockwise",!1),n.Factory.backCompat(l,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"})},9480:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RGB=void 0;let n=r(8949),i=r(913),a=r(6394);t.RGB=function(e){let t=e.data,r=t.length,n=this.red(),i=this.green(),a=this.blue();for(let e=0;e<r;e+=4){let r=(.34*t[e]+.5*t[e+1]+.16*t[e+2])/255;t[e]=r*n,t[e+1]=r*i,t[e+2]=r*a,t[e+3]=t[e+3]}},n.Factory.addGetterSetter(i.Node,"red",0,function(e){return(this._filterUpToDate=!1,e>255)?255:e<0?0:Math.round(e)}),n.Factory.addGetterSetter(i.Node,"green",0,function(e){return(this._filterUpToDate=!1,e>255)?255:e<0?0:Math.round(e)}),n.Factory.addGetterSetter(i.Node,"blue",0,a.RGBComponent,n.Factory.afterSetFilter)},9482:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Tag=t.Label=void 0;let n=r(8949),i=r(3306),a=r(836),o=r(6394),s=r(6878),l=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],d="right",h="down",c="left",u=l.length;class f extends a.Group{constructor(e){super(e),this.on("add.konva",function(e){this._addListeners(e.child),this._sync()})}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(e){let t=this,r,n=function(){t._sync()};for(r=0;r<u;r++)e.on(l[r]+"Change.konva",n)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){let e=this.getText(),t=this.getTag(),r,n,i,a,o,s,l;if(e&&t){switch(r=e.width(),n=e.height(),i=t.pointerDirection(),a=t.pointerWidth(),l=t.pointerHeight(),o=0,s=0,i){case"up":o=r/2,s=-1*l;break;case d:o=r+a,s=n/2;break;case h:o=r/2,s=n+l;break;case c:o=-1*a,s=n/2}t.setAttrs({x:-1*o,y:-1*s,width:r,height:n}),e.setAttrs({x:-1*o,y:-1*s})}}}t.Label=f,f.prototype.className="Label",(0,s._registerNode)(f);class p extends i.Shape{_sceneFunc(e){let t=this.width(),r=this.height(),n=this.pointerDirection(),i=this.pointerWidth(),a=this.pointerHeight(),o=this.cornerRadius(),s=0,l=0,u=0,f=0;"number"==typeof o?s=l=u=f=Math.min(o,t/2,r/2):(s=Math.min(o[0]||0,t/2,r/2),l=Math.min(o[1]||0,t/2,r/2),f=Math.min(o[2]||0,t/2,r/2),u=Math.min(o[3]||0,t/2,r/2)),e.beginPath(),e.moveTo(s,0),"up"===n&&(e.lineTo((t-i)/2,0),e.lineTo(t/2,-1*a),e.lineTo((t+i)/2,0)),e.lineTo(t-l,0),e.arc(t-l,l,l,3*Math.PI/2,0,!1),n===d&&(e.lineTo(t,(r-a)/2),e.lineTo(t+i,r/2),e.lineTo(t,(r+a)/2)),e.lineTo(t,r-f),e.arc(t-f,r-f,f,0,Math.PI/2,!1),n===h&&(e.lineTo((t+i)/2,r),e.lineTo(t/2,r+a),e.lineTo((t-i)/2,r)),e.lineTo(u,r),e.arc(u,r-u,u,Math.PI/2,Math.PI,!1),n===c&&(e.lineTo(0,(r+a)/2),e.lineTo(-1*i,r/2),e.lineTo(0,(r-a)/2)),e.lineTo(0,s),e.arc(s,s,s,Math.PI,3*Math.PI/2,!1),e.closePath(),e.fillStrokeShape(this)}getSelfRect(){let e=0,t=0,r=this.pointerWidth(),n=this.pointerHeight(),i=this.pointerDirection(),a=this.width(),o=this.height();return"up"===i?(t-=n,o+=n):i===h?o+=n:i===c?(e-=1.5*r,a+=r):i===d&&(a+=1.5*r),{x:e,y:t,width:a,height:o}}}t.Tag=p,p.prototype.className="Tag",(0,s._registerNode)(p),n.Factory.addGetterSetter(p,"pointerDirection","none"),n.Factory.addGetterSetter(p,"pointerWidth",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(p,"pointerHeight",0,(0,o.getNumberValidator)()),n.Factory.addGetterSetter(p,"cornerRadius",0,(0,o.getNumberOrArrayOfNumbersValidator)(4))},9490:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Posterize=void 0;let n=r(8949),i=r(913),a=r(6394);t.Posterize=function(e){let t=Math.round(254*this.levels())+1,r=e.data,n=r.length,i=255/t;for(let e=0;e<n;e+=1)r[e]=Math.floor(r[e]/i)*i},n.Factory.addGetterSetter(i.Node,"levels",.5,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},9688:(e,t,r)=>{r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),a=i?n(e.slice(1),i):void 0;if(a)return a;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},i=/^\[(.+)\]$/,a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:o(t,e)).classGroupId=r;return}if("function"==typeof e)return s(e)?void a(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,i])=>{a(i,o(t,e),r,n)})})},o=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},s=e=>e.isThemeGetter,l=/\s+/;function d(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=h(e))&&(n&&(n+=" "),n+=t);return n}let h=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=h(e[n]))&&(r&&(r+=" "),r+=t);return r},c=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},u=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,g=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,m=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,v=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>p.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),_=e=>!!e&&Number.isInteger(Number(e)),S=e=>e.endsWith("%")&&w(e.slice(0,-1)),C=e=>g.test(e),k=()=>!0,P=e=>m.test(e)&&!v.test(e),A=()=>!1,M=e=>y.test(e),T=e=>b.test(e),E=e=>!D(e)&&!G(e),R=e=>W(e,X,A),D=e=>u.test(e),N=e=>W(e,$,P),F=e=>W(e,Z,w),O=e=>W(e,Y,A),L=e=>W(e,q,T),I=e=>W(e,J,M),G=e=>f.test(e),j=e=>K(e,$),U=e=>K(e,Q),B=e=>K(e,Y),z=e=>K(e,X),H=e=>K(e,q),V=e=>K(e,J,!0),W=(e,t,r)=>{let n=u.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},K=(e,t,r=!1)=>{let n=f.exec(e);return!!n&&(n[1]?t(n[1]):r)},Y=e=>"position"===e||"percentage"===e,q=e=>"image"===e||"url"===e,X=e=>"length"===e||"size"===e||"bg-size"===e,$=e=>"length"===e,Z=e=>"number"===e,Q=e=>"family-name"===e,J=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,o,s,h=function(l){let d;return o=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,a)=>{r.set(i,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}})((d=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],i=0,a=0,o=0;for(let r=0;r<e.length;r++){let s=e[r];if(0===i&&0===a){if(":"===s){n.push(e.slice(o,r)),o=r+1;continue}if("/"===s){t=r;continue}}"["===s?i++:"]"===s?i--:"("===s?a++:")"===s&&a--}let s=0===n.length?e:e.substring(o),l=(r=s).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:l!==s,baseClassName:l,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(d),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(d),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)a(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}})(d)}).cache.get,s=r.cache.set,h=c,c(l)};function c(e){let t=o(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:a}=t,o=[],s=e.trim().split(l),d="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:l,modifiers:h,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:f}=r(t);if(l){d=t+(d.length>0?" "+d:d);continue}let p=!!f,g=n(p?u.substring(0,f):u);if(!g){if(!p||!(g=n(u))){d=t+(d.length>0?" "+d:d);continue}p=!1}let m=a(h).join(":"),v=c?m+"!":m,y=v+g;if(o.includes(y))continue;o.push(y);let b=i(g,p);for(let e=0;e<b.length;++e){let t=b[e];o.push(v+t)}d=t+(d.length>0?" "+d:d)}return d})(e,r);return s(e,n),n}return function(){return h(d.apply(null,arguments))}}(()=>{let e=c("color"),t=c("font"),r=c("text"),n=c("font-weight"),i=c("tracking"),a=c("leading"),o=c("breakpoint"),s=c("container"),l=c("spacing"),d=c("radius"),h=c("shadow"),u=c("inset-shadow"),f=c("text-shadow"),p=c("drop-shadow"),g=c("blur"),m=c("perspective"),v=c("aspect"),y=c("ease"),b=c("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],A=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],M=()=>[...A(),G,D],T=()=>["auto","hidden","clip","visible","scroll"],W=()=>["auto","contain","none"],K=()=>[G,D,l],Y=()=>[x,"full","auto",...K()],q=()=>[_,"none","subgrid",G,D],X=()=>["auto",{span:["full",_,G,D]},_,G,D],$=()=>[_,"auto",G,D],Z=()=>["auto","min","max","fr",G,D],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...K()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],er=()=>[e,G,D],en=()=>[...A(),B,O,{position:[G,D]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",z,R,{size:[G,D]}],eo=()=>[S,j,N],es=()=>["","none","full",d,G,D],el=()=>["",w,j,N],ed=()=>["solid","dashed","dotted","double"],eh=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ec=()=>[w,S,B,O],eu=()=>["","none",g,G,D],ef=()=>["none",w,G,D],ep=()=>["none",w,G,D],eg=()=>[w,G,D],em=()=>[x,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[k],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",w],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,D,G,v]}],container:["container"],columns:[{columns:[w,D,G,s]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:M()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Y()}],"inset-x":[{"inset-x":Y()}],"inset-y":[{"inset-y":Y()}],start:[{start:Y()}],end:[{end:Y()}],top:[{top:Y()}],right:[{right:Y()}],bottom:[{bottom:Y()}],left:[{left:Y()}],visibility:["visible","invisible","collapse"],z:[{z:[_,"auto",G,D]}],basis:[{basis:[x,"full","auto",s,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",D]}],grow:[{grow:["",w,G,D]}],shrink:[{shrink:["",w,G,D]}],order:[{order:[_,"first","last","none",G,D]}],"grid-cols":[{"grid-cols":q()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":q()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[o]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,j,N]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,G,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",S,D]}],"font-family":[{font:[U,D,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,G,D]}],"line-clamp":[{"line-clamp":[w,"none",G,F]}],leading:[{leading:[a,...K()]}],"list-image":[{"list-image":["none",G,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",G,N]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[w,"auto",G,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_,G,D],radial:["",G,D],conic:[_,G,D]},H,L]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,G,D]}],"outline-w":[{outline:["",w,j,N]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",h,V,I]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",u,V,I]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[w,N]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,V,I]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[w,G,D]}],"mix-blend":[{"mix-blend":[...eh(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eh()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":ec()}],"mask-image-linear-to-pos":[{"mask-linear-to":ec()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ec()}],"mask-image-t-to-pos":[{"mask-t-to":ec()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ec()}],"mask-image-r-to-pos":[{"mask-r-to":ec()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ec()}],"mask-image-b-to-pos":[{"mask-b-to":ec()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ec()}],"mask-image-l-to-pos":[{"mask-l-to":ec()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ec()}],"mask-image-x-to-pos":[{"mask-x-to":ec()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ec()}],"mask-image-y-to-pos":[{"mask-y-to":ec()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[G,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":ec()}],"mask-image-radial-to-pos":[{"mask-radial-to":ec()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":A()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":ec()}],"mask-image-conic-to-pos":[{"mask-conic-to":ec()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,D]}],filter:[{filter:["","none",G,D]}],blur:[{blur:eu()}],brightness:[{brightness:[w,G,D]}],contrast:[{contrast:[w,G,D]}],"drop-shadow":[{"drop-shadow":["","none",p,V,I]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",w,G,D]}],"hue-rotate":[{"hue-rotate":[w,G,D]}],invert:[{invert:["",w,G,D]}],saturate:[{saturate:[w,G,D]}],sepia:[{sepia:["",w,G,D]}],"backdrop-filter":[{"backdrop-filter":["","none",G,D]}],"backdrop-blur":[{"backdrop-blur":eu()}],"backdrop-brightness":[{"backdrop-brightness":[w,G,D]}],"backdrop-contrast":[{"backdrop-contrast":[w,G,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,G,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,G,D]}],"backdrop-invert":[{"backdrop-invert":["",w,G,D]}],"backdrop-opacity":[{"backdrop-opacity":[w,G,D]}],"backdrop-saturate":[{"backdrop-saturate":[w,G,D]}],"backdrop-sepia":[{"backdrop-sepia":["",w,G,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",G,D]}],ease:[{ease:["linear","initial",y,G,D]}],delay:[{delay:[w,G,D]}],animate:[{animate:["none",b,G,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,G,D]}],"perspective-origin":[{"perspective-origin":M()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[G,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:M()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:em()}],"translate-x":[{"translate-x":em()}],"translate-y":[{"translate-y":em()}],"translate-z":[{"translate-z":em()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,D]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[w,j,N,F]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9705:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Mask=void 0;let n=r(8949),i=r(913),a=r(6394);function o(e,t,r){let n=(r*e.width+t)*4,i=[];return i.push(e.data[n++],e.data[n++],e.data[n++],e.data[n++]),i}function s(e,t){return Math.sqrt(Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2)+Math.pow(e[2]-t[2],2))}t.Mask=function(e){let t=function(e,t){let r=o(e,0,0),n=o(e,e.width-1,0),i=o(e,0,e.height-1),a=o(e,e.width-1,e.height-1),l=t||10;if(s(r,n)<l&&s(n,a)<l&&s(a,i)<l&&s(i,r)<l){let t=function(e){let t=[0,0,0];for(let r=0;r<e.length;r++)t[0]+=e[r][0],t[1]+=e[r][1],t[2]+=e[r][2];return t[0]/=e.length,t[1]/=e.length,t[2]/=e.length,t}([n,r,a,i]),o=[];for(let r=0;r<e.width*e.height;r++){let n=s(t,[e.data[4*r],e.data[4*r+1],e.data[4*r+2]]);o[r]=n<l?0:255}return o}}(e,this.threshold());if(t){var r=t=function(e,t,r){let n=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],i=Math.round(Math.sqrt(n.length)),a=Math.floor(i/2),o=[];for(let s=0;s<r;s++)for(let l=0;l<t;l++){let d=s*t+l,h=0;for(let o=0;o<i;o++)for(let d=0;d<i;d++){let c=s+o-a,u=l+d-a;if(c>=0&&c<r&&u>=0&&u<t){let r=c*t+u,a=n[o*i+d];h+=e[r]*a}}o[d]=h}return o}(t=function(e,t,r){let n=[1,1,1,1,1,1,1,1,1],i=Math.round(Math.sqrt(n.length)),a=Math.floor(i/2),o=[];for(let s=0;s<r;s++)for(let l=0;l<t;l++){let d=s*t+l,h=0;for(let o=0;o<i;o++)for(let d=0;d<i;d++){let c=s+o-a,u=l+d-a;if(c>=0&&c<r&&u>=0&&u<t){let r=c*t+u,a=n[o*i+d];h+=e[r]*a}}o[d]=255*(h>=1020)}return o}(t=function(e,t,r){let n=[1,1,1,1,0,1,1,1,1],i=Math.round(Math.sqrt(n.length)),a=Math.floor(i/2),o=[];for(let s=0;s<r;s++)for(let l=0;l<t;l++){let d=s*t+l,h=0;for(let o=0;o<i;o++)for(let d=0;d<i;d++){let c=s+o-a,u=l+d-a;if(c>=0&&c<r&&u>=0&&u<t){let r=c*t+u,a=n[o*i+d];h+=e[r]*a}}o[d]=255*(2040===h)}return o}(t,e.width,e.height),e.width,e.height),e.width,e.height);for(let t=0;t<e.width*e.height;t++)e.data[4*t+3]=r[t]}return e},n.Factory.addGetterSetter(i.Node,"threshold",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},9708:(e,t,r)=>{r.d(t,{DX:()=>s,TL:()=>o});var n=r(2115),i=r(6101),a=r(5155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var o;let e,s,l=(o=r,(s=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(s=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],a=t[n];/^on[A-Z]/.test(n)?i&&a?r[n]=(...e)=>{let t=a(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...a}:"className"===n&&(r[n]=[i,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,i.t)(t,l):l),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...o}=e,s=n.Children.toArray(i),l=s.find(d);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(t,{...o,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var s=o("Slot"),l=Symbol("radix.slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9732:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Blur=void 0;let n=r(8949),i=r(913),a=r(6394);function o(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}let s=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],l=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];t.Blur=function(e){let t=Math.round(this.blurRadius());t>0&&function(e,t){let r,n,i,a,d,h,c,u,f,p,g,m,v,y,b,x,w,_,S,C,k=e.data,P=e.width,A=e.height,M=t+t+1,T=P-1,E=A-1,R=t+1,D=R*(R+1)/2,N=new o,F=s[t],O=l[t],L=null,I=N,G=null,j=null;for(let e=1;e<M;e++)I=I.next=new o,e===R&&(L=I);I.next=N,i=n=0;for(let e=0;e<A;e++){m=v=y=b=a=d=h=c=0,u=R*(x=k[n]),f=R*(w=k[n+1]),p=R*(_=k[n+2]),g=R*(S=k[n+3]),a+=D*x,d+=D*w,h+=D*_,c+=D*S,I=N;for(let e=0;e<R;e++)I.r=x,I.g=w,I.b=_,I.a=S,I=I.next;for(let e=1;e<R;e++)r=n+((T<e?T:e)<<2),a+=(I.r=x=k[r])*(C=R-e),d+=(I.g=w=k[r+1])*C,h+=(I.b=_=k[r+2])*C,c+=(I.a=S=k[r+3])*C,m+=x,v+=w,y+=_,b+=S,I=I.next;G=N,j=L;for(let e=0;e<P;e++)k[n+3]=S=c*F>>O,0!==S?(S=255/S,k[n]=(a*F>>O)*S,k[n+1]=(d*F>>O)*S,k[n+2]=(h*F>>O)*S):k[n]=k[n+1]=k[n+2]=0,a-=u,d-=f,h-=p,c-=g,u-=G.r,f-=G.g,p-=G.b,g-=G.a,r=i+((r=e+t+1)<T?r:T)<<2,m+=G.r=k[r],v+=G.g=k[r+1],y+=G.b=k[r+2],b+=G.a=k[r+3],a+=m,d+=v,h+=y,c+=b,G=G.next,u+=x=j.r,f+=w=j.g,p+=_=j.b,g+=S=j.a,m-=x,v-=w,y-=_,b-=S,j=j.next,n+=4;i+=P}for(let e=0;e<P;e++){v=y=b=m=d=h=c=a=0,u=R*(x=k[n=e<<2]),f=R*(w=k[n+1]),p=R*(_=k[n+2]),g=R*(S=k[n+3]),a+=D*x,d+=D*w,h+=D*_,c+=D*S,I=N;for(let e=0;e<R;e++)I.r=x,I.g=w,I.b=_,I.a=S,I=I.next;let i=P;for(let r=1;r<=t;r++)n=i+e<<2,a+=(I.r=x=k[n])*(C=R-r),d+=(I.g=w=k[n+1])*C,h+=(I.b=_=k[n+2])*C,c+=(I.a=S=k[n+3])*C,m+=x,v+=w,y+=_,b+=S,I=I.next,r<E&&(i+=P);n=e,G=N,j=L;for(let t=0;t<A;t++)k[(r=n<<2)+3]=S=c*F>>O,S>0?(S=255/S,k[r]=(a*F>>O)*S,k[r+1]=(d*F>>O)*S,k[r+2]=(h*F>>O)*S):k[r]=k[r+1]=k[r+2]=0,a-=u,d-=f,h-=p,c-=g,u-=G.r,f-=G.g,p-=G.b,g-=G.a,r=e+((r=t+R)<E?r:E)*P<<2,a+=m+=G.r=k[r],d+=v+=G.g=k[r+1],h+=y+=G.b=k[r+2],c+=b+=G.a=k[r+3],G=G.next,u+=x=j.r,f+=w=j.g,p+=_=j.b,g+=S=j.a,m-=x,v-=w,y-=_,b-=S,j=j.next,n+=P}}(e,t)},n.Factory.addGetterSetter(i.Node,"blurRadius",0,(0,a.getNumberValidator)(),n.Factory.afterSetFilter)},9869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9902:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Emboss=void 0;let n=r(8949),i=r(913),a=r(6509),o=r(6394);t.Emboss=function(e){let t=10*this.embossStrength(),r=255*this.embossWhiteLevel(),n=this.embossDirection(),i=this.embossBlend(),o=e.data,s=e.width,l=e.height,d=4*s,h=0,c=0,u=l;switch(n){case"top-left":h=-1,c=-1;break;case"top":h=-1,c=0;break;case"top-right":h=-1,c=1;break;case"right":h=0,c=1;break;case"bottom-right":h=1,c=1;break;case"bottom":h=1,c=0;break;case"bottom-left":h=1,c=-1;break;case"left":h=0,c=-1;break;default:a.Util.error("Unknown emboss direction: "+n)}do{let e=(u-1)*d,n=h;u+n<1&&(n=0),u+n>l&&(n=0);let a=(u-1+n)*s*4,f=s;do{let n=e+(f-1)*4,l=c;f+l<1&&(l=0),f+l>s&&(l=0);let d=a+(f-1+l)*4,h=o[n]-o[d],u=o[n+1]-o[d+1],p=o[n+2]-o[d+2],g=h,m=g>0?g:-g,v=p>0?p:-p;if((u>0?u:-u)>m&&(g=u),v>m&&(g=p),g*=t,i){let e=o[n]+g,t=o[n+1]+g,r=o[n+2]+g;o[n]=e>255?255:e<0?0:e,o[n+1]=t>255?255:t<0?0:t,o[n+2]=r>255?255:r<0?0:r}else{let e=r-g;e<0?e=0:e>255&&(e=255),o[n]=o[n+1]=o[n+2]=e}}while(--f)}while(--u)},n.Factory.addGetterSetter(i.Node,"embossStrength",.5,(0,o.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"embossWhiteLevel",.5,(0,o.getNumberValidator)(),n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"embossDirection","top-left",void 0,n.Factory.afterSetFilter),n.Factory.addGetterSetter(i.Node,"embossBlend",!1,void 0,n.Factory.afterSetFilter)},9946:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:d="",children:h,iconNode:c,...u}=e;return(0,n.createElement)("svg",{ref:t,...o,width:i,height:i,stroke:r,strokeWidth:l?24*Number(s)/Number(i):s,className:a("lucide",d),...!h&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(h)?h:[h]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:l,...d}=r;return(0,n.createElement)(s,{ref:o,iconNode:t,className:a("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...d})});return r.displayName=i(e),r}},9954:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("shapes",[["path",{d:"M8.3 10a.7.7 0 0 1-.626-1.079L11.4 3a.7.7 0 0 1 1.198-.043L16.3 8.9a.7.7 0 0 1-.572 1.1Z",key:"1bo67w"}],["rect",{x:"3",y:"14",width:"7",height:"7",rx:"1",key:"1bkyp8"}],["circle",{cx:"17.5",cy:"17.5",r:"3.5",key:"w3z12y"}]])},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return u},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return d},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function h(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function c(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await c(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&d(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let u="undefined"!=typeof performance,f=u&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);