(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>d});var s=t(5155),l=t(2115),r=t(9708),n=t(2085),i=t(9434);let c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,a)=>{let{className:t,variant:l,size:n,asChild:d=!1,...o}=e,m=d?r.DX:"button";return(0,s.jsx)(m,{className:(0,i.cn)(c({variant:l,size:n,className:t})),ref:a,...o})});d.displayName="Button"},353:(e,a,t)=>{"use strict";t.d(a,{UserMenu:()=>O});var s=t(5155),l=t(2115),r=t(7606),n=t(285),i=t(8698),c=t(3052),d=t(9434);let o=i.bL,m=i.l9;i.YJ,i.ZL,i.Pb,i.z6,l.forwardRef((e,a)=>{let{className:t,inset:l,children:r,...n}=e;return(0,s.jsxs)(i.ZP,{ref:a,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",t),...n,children:[r,(0,s.jsx)(c.A,{className:"ml-auto h-4 w-4"})]})}).displayName=i.ZP.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(i.G5,{ref:a,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})}).displayName=i.G5.displayName;let u=l.forwardRef((e,a)=>{let{className:t,sideOffset:l=4,...r}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsx)(i.UC,{ref:a,sideOffset:l,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...r})})});u.displayName=i.UC.displayName;let h=l.forwardRef((e,a)=>{let{className:t,inset:l,...r}=e;return(0,s.jsx)(i.q7,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",t),...r})});h.displayName=i.q7.displayName;let x=l.forwardRef((e,a)=>{let{className:t,inset:l,...r}=e;return(0,s.jsx)(i.JU,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",t),...r})});x.displayName=i.JU.displayName;let p=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(i.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...l})});p.displayName=i.wv.displayName;var f=t(4011);let g=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(f.bL,{ref:a,className:(0,d.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...l})});g.displayName=f.bL.displayName;let j=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(f._V,{ref:a,className:(0,d.cn)("aspect-square h-full w-full",t),...l})});j.displayName=f._V.displayName;let v=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(f.H4,{ref:a,className:(0,d.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...l})});v.displayName=f.H4.displayName;var y=t(1007),b=t(7213),N=t(1976),w=t(381),C=t(4835),k=t(4165),A=t(2523),S=t(5057),E=t(6695);let _=(0,t(2085).F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),I=l.forwardRef((e,a)=>{let{className:t,variant:l,...r}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,d.cn)(_({variant:l}),t),...r})});I.displayName="Alert",l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("h5",{ref:a,className:(0,d.cn)("mb-1 font-medium leading-none tracking-tight",t),...l})}).displayName="AlertTitle";let z=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",t),...l})});z.displayName="AlertDescription";var R=t(1154);function T(e){let{onToggleMode:a,onSuccess:t}=e,[i,c]=(0,l.useState)(""),[d,o]=(0,l.useState)(""),[m,u]=(0,l.useState)(!1),[h,x]=(0,l.useState)(""),{signIn:p}=(0,r.A)(),f=async e=>{e.preventDefault(),u(!0),x("");try{await p(i,d),null==t||t()}catch(e){x(e.message||"登入失敗，請檢查您的帳號密碼")}finally{u(!1)}};return(0,s.jsxs)(E.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(E.aR,{children:[(0,s.jsx)(E.ZB,{children:"登入"}),(0,s.jsx)(E.BT,{children:"登入您的帳號開始創作早安圖"})]}),(0,s.jsx)(E.Wu,{children:(0,s.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[h&&(0,s.jsx)(I,{variant:"destructive",children:(0,s.jsx)(z,{children:h})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"email",children:"電子郵件"}),(0,s.jsx)(A.p,{id:"email",type:"email",value:i,onChange:e=>c(e.target.value),placeholder:"請輸入您的電子郵件",required:!0,disabled:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"password",children:"密碼"}),(0,s.jsx)(A.p,{id:"password",type:"password",value:d,onChange:e=>o(e.target.value),placeholder:"請輸入您的密碼",required:!0,disabled:m})]}),(0,s.jsxs)(n.$,{type:"submit",className:"w-full",disabled:m,children:[m&&(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4 animate-spin"}),"登入"]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:a,className:"text-sm text-blue-600 hover:underline",disabled:m,children:"還沒有帳號？立即註冊"})})]})})]})}function D(e){let{onToggleMode:a,onSuccess:t}=e,[i,c]=(0,l.useState)(""),[d,o]=(0,l.useState)(""),[m,u]=(0,l.useState)(""),[h,x]=(0,l.useState)(""),[p,f]=(0,l.useState)(""),[g,j]=(0,l.useState)(!1),[v,y]=(0,l.useState)(""),[b,N]=(0,l.useState)(!1),{signUp:w}=(0,r.A)(),C=async e=>{if(e.preventDefault(),j(!0),y(""),N(!1),d!==m){y("密碼確認不符"),j(!1);return}if(d.length<6){y("密碼長度至少需要 6 個字元"),j(!1);return}try{await w(i,d,{username:h||void 0,full_name:p||void 0}),N(!0),setTimeout(()=>{null==t||t()},2e3)}catch(e){y(e.message||"註冊失敗，請稍後再試")}finally{j(!1)}};return b?(0,s.jsx)(E.Zp,{className:"w-full max-w-md mx-auto",children:(0,s.jsx)(E.Wu,{className:"pt-6",children:(0,s.jsx)(I,{children:(0,s.jsx)(z,{children:"註冊成功！請檢查您的電子郵件並點擊確認連結來啟用帳號。"})})})}):(0,s.jsxs)(E.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(E.aR,{children:[(0,s.jsx)(E.ZB,{children:"註冊"}),(0,s.jsx)(E.BT,{children:"建立新帳號開始您的創作之旅"})]}),(0,s.jsx)(E.Wu,{children:(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[v&&(0,s.jsx)(I,{variant:"destructive",children:(0,s.jsx)(z,{children:v})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"email",children:"電子郵件 *"}),(0,s.jsx)(A.p,{id:"email",type:"email",value:i,onChange:e=>c(e.target.value),placeholder:"請輸入您的電子郵件",required:!0,disabled:g})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"fullName",children:"姓名"}),(0,s.jsx)(A.p,{id:"fullName",type:"text",value:p,onChange:e=>f(e.target.value),placeholder:"請輸入您的姓名",disabled:g})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"username",children:"使用者名稱"}),(0,s.jsx)(A.p,{id:"username",type:"text",value:h,onChange:e=>x(e.target.value),placeholder:"請輸入使用者名稱",disabled:g})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"password",children:"密碼 *"}),(0,s.jsx)(A.p,{id:"password",type:"password",value:d,onChange:e=>o(e.target.value),placeholder:"請輸入密碼 (至少 6 個字元)",required:!0,disabled:g})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(S.J,{htmlFor:"confirmPassword",children:"確認密碼 *"}),(0,s.jsx)(A.p,{id:"confirmPassword",type:"password",value:m,onChange:e=>u(e.target.value),placeholder:"請再次輸入密碼",required:!0,disabled:g})]}),(0,s.jsxs)(n.$,{type:"submit",className:"w-full",disabled:g,children:[g&&(0,s.jsx)(R.A,{className:"mr-2 h-4 w-4 animate-spin"}),"註冊"]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:a,className:"text-sm text-blue-600 hover:underline",disabled:g,children:"已有帳號？立即登入"})})]})})]})}function L(e){let{open:a,onOpenChange:t,defaultMode:r="login"}=e,[n,i]=(0,l.useState)(r),c=()=>{i("login"===n?"signup":"login")},d=()=>{t(!1)};return(0,s.jsx)(k.lG,{open:a,onOpenChange:t,children:(0,s.jsx)(k.Cf,{className:"sm:max-w-md",children:"login"===n?(0,s.jsx)(T,{onToggleMode:c,onSuccess:d}):(0,s.jsx)(D,{onToggleMode:c,onSuccess:d})})})}var J=t(6874),P=t.n(J);function O(){var e;let{user:a,profile:t,signOut:i}=(0,r.A)(),[c,d]=(0,l.useState)(!1),[f,k]=(0,l.useState)("login"),A=async()=>{try{await i()}catch(e){console.error("Sign out error:",e)}},S=e=>{k(e),d(!0)};if(!a)return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",onClick:()=>S("login"),children:"登入"}),(0,s.jsx)(n.$,{onClick:()=>S("signup"),children:"註冊"})]}),(0,s.jsx)(L,{open:c,onOpenChange:d,defaultMode:f})]});let E=(null==t?void 0:t.full_name)||(null==t?void 0:t.username)||(null==(e=a.email)?void 0:e.split("@")[0])||"使用者",_=E.charAt(0).toUpperCase();return(0,s.jsxs)(o,{children:[(0,s.jsx)(m,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(g,{className:"h-8 w-8",children:[(0,s.jsx)(j,{src:null==t?void 0:t.avatar_url,alt:E}),(0,s.jsx)(v,{children:_})]})})}),(0,s.jsxs)(u,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(x,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:E}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:a.email})]})}),(0,s.jsx)(p,{}),(0,s.jsx)(h,{asChild:!0,children:(0,s.jsxs)(P(),{href:"/profile",className:"cursor-pointer",children:[(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"個人資料"})]})}),(0,s.jsx)(h,{asChild:!0,children:(0,s.jsxs)(P(),{href:"/my-creations",className:"cursor-pointer",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"我的作品"})]})}),(0,s.jsx)(h,{asChild:!0,children:(0,s.jsxs)(P(),{href:"/favorites",className:"cursor-pointer",children:[(0,s.jsx)(N.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"我的收藏"})]})}),(0,s.jsx)(h,{asChild:!0,children:(0,s.jsxs)(P(),{href:"/settings",className:"cursor-pointer",children:[(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"設定"})]})}),(0,s.jsx)(p,{}),(0,s.jsxs)(h,{onClick:A,className:"cursor-pointer",children:[(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"登出"})]})]})]})}},2099:(e,a,t)=>{"use strict";t.d(a,{i:()=>i});var s=t(3865),l=t(9509);let r=l.env.NEXT_PUBLIC_SUPABASE_URL,n=l.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,i=()=>(0,s.createBrowserClient)(r,n)},2523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(5155),l=t(2115),r=t(9434);let n=l.forwardRef((e,a)=>{let{className:t,type:l,...n}=e;return(0,s.jsx)("input",{type:l,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...n})});n.displayName="Input"},4165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,L3:()=>h,c7:()=>u,lG:()=>c});var s=t(5155),l=t(2115),r=t(5452),n=t(4416),i=t(9434);let c=r.bL;r.l9;let d=r.ZL;r.bm;let o=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});o.displayName=r.hJ.displayName;let m=l.forwardRef((e,a)=>{let{className:t,children:l,...c}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(o,{}),(0,s.jsxs)(r.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[l,(0,s.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.UC.displayName;let u=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};u.displayName="DialogHeader";let h=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...l})});h.displayName=r.hE.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...l})}).displayName=r.VY.displayName},5057:(e,a,t)=>{"use strict";t.d(a,{J:()=>d});var s=t(5155),l=t(2115),r=t(968),n=t(2085),i=t(9434);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(r.b,{ref:a,className:(0,i.cn)(c(),t),...l})});d.displayName=r.b.displayName},6695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>i});var s=t(5155),l=t(2115),r=t(9434);let n=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});n.displayName="Card";let i=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...l})});i.displayName="CardHeader";let c=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-muted-foreground",t),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",t),...l})});o.displayName="CardContent",l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",t),...l})}).displayName="CardFooter"},7369:(e,a,t)=>{"use strict";t.d(a,{Editor:()=>eq});var s=t(5155),l=t(2115),r=t(9434);let n={width:800,height:600,background:{type:"color",value:"#ffffff"},objects:[],texts:[]},i={tool:"select",selectedObjectId:null,canvasData:n,history:[n],historyIndex:0,isLoading:!1};function c(e,a){let t=a=>{let t=e.history.slice(0,e.historyIndex+1);return t.push(a),{history:t.slice(-20),historyIndex:Math.min(t.length-1,19)}};switch(a.type){case"SET_TOOL":return{...e,tool:a.payload};case"SELECT_OBJECT":return{...e,selectedObjectId:a.payload};case"ADD_TEXT_LAYER":{let s={id:(0,r.$C)(),type:"text",content:a.payload.text,x:a.payload.x,y:a.payload.y,fontSize:24,fontFamily:"Arial",fontWeight:"normal",color:"#000000",textAlign:"left",rotation:0,opacity:1,zIndex:Math.max(...e.canvasData.texts.map(e=>e.zIndex),0)+1},l={...e.canvasData,texts:[...e.canvasData.texts,s]};return{...e,canvasData:l,selectedObjectId:s.id,...t(l)}}case"ADD_OBJECT_LAYER":{let s={id:(0,r.$C)(),type:"image",src:a.payload.src,x:a.payload.x,y:a.payload.y,width:200,height:200,rotation:0,opacity:1,flipX:!1,flipY:!1,zIndex:Math.max(...e.canvasData.objects.map(e=>e.zIndex),0)+1},l={...e.canvasData,objects:[...e.canvasData.objects,s]};return{...e,canvasData:l,selectedObjectId:s.id,...t(l)}}case"UPDATE_TEXT_LAYER":{let s={...e.canvasData,texts:e.canvasData.texts.map(e=>e.id===a.payload.id?{...e,...a.payload.updates}:e)};return{...e,canvasData:s,...t(s)}}case"UPDATE_OBJECT_LAYER":{let s={...e.canvasData,objects:e.canvasData.objects.map(e=>e.id===a.payload.id?{...e,...a.payload.updates}:e)};return{...e,canvasData:s,...t(s)}}case"DELETE_LAYER":{let s={...e.canvasData,texts:e.canvasData.texts.filter(e=>e.id!==a.payload),objects:e.canvasData.objects.filter(e=>e.id!==a.payload)};return{...e,canvasData:s,selectedObjectId:e.selectedObjectId===a.payload?null:e.selectedObjectId,...t(s)}}case"SET_BACKGROUND":{let s={...e.canvasData,background:a.payload};return{...e,canvasData:s,...t(s)}}case"UNDO":if(e.historyIndex>0){let a=e.historyIndex-1;return{...e,canvasData:e.history[a],historyIndex:a,selectedObjectId:null}}return e;case"REDO":if(e.historyIndex<e.history.length-1){let a=e.historyIndex+1;return{...e,canvasData:e.history[a],historyIndex:a,selectedObjectId:null}}return e;case"CLEAR_CANVAS":{let a={...n};return{...e,canvasData:a,selectedObjectId:null,...t(a)}}case"LOAD_CANVAS_DATA":return{...e,canvasData:a.payload,selectedObjectId:null,...t(a.payload)};case"SET_LOADING":return{...e,isLoading:a.payload};default:return e}}let d=(0,l.createContext)(void 0);function o(e){let{children:a}=e,[t,s]=(0,l.useReducer)(c,i),r=(0,l.useCallback)(e=>{s({type:"SET_TOOL",payload:e})},[]),n=(0,l.useCallback)(e=>{s({type:"SELECT_OBJECT",payload:e})},[]),o=(0,l.useCallback)((e,a,t)=>{s({type:"ADD_TEXT_LAYER",payload:{text:e,x:a,y:t}})},[]),m=(0,l.useCallback)((e,a,t)=>{s({type:"ADD_OBJECT_LAYER",payload:{src:e,x:a,y:t}})},[]),u=(0,l.useCallback)((e,a)=>{s({type:"UPDATE_TEXT_LAYER",payload:{id:e,updates:a}})},[]),h=(0,l.useCallback)((e,a)=>{s({type:"UPDATE_OBJECT_LAYER",payload:{id:e,updates:a}})},[]),x=(0,l.useCallback)(e=>{s({type:"DELETE_LAYER",payload:e})},[]),p=(0,l.useCallback)(e=>{s({type:"SET_BACKGROUND",payload:e})},[]),f=(0,l.useCallback)((e,a,s)=>{t.canvasData.texts.some(a=>a.id===e)?u(e,{x:a,y:s}):h(e,{x:a,y:s})},[t.canvasData.texts,u,h]),g=(0,l.useCallback)((e,a,t)=>{h(e,{width:a,height:t})},[h]),j=(0,l.useCallback)((e,a)=>{t.canvasData.texts.some(a=>a.id===e)?u(e,{rotation:a}):h(e,{rotation:a})},[t.canvasData.texts,u,h]),v=(0,l.useCallback)((e,a)=>{t.canvasData.texts.some(a=>a.id===e)?u(e,{opacity:a}):h(e,{opacity:a})},[t.canvasData.texts,u,h]),y=(0,l.useCallback)(e=>{let a=Math.max(...t.canvasData.texts.map(e=>e.zIndex),...t.canvasData.objects.map(e=>e.zIndex));t.canvasData.texts.some(a=>a.id===e)?u(e,{zIndex:a+1}):h(e,{zIndex:a+1})},[t.canvasData,u,h]),b=(0,l.useCallback)(e=>{let a=Math.min(...t.canvasData.texts.map(e=>e.zIndex),...t.canvasData.objects.map(e=>e.zIndex));t.canvasData.texts.some(a=>a.id===e)?u(e,{zIndex:a-1}):h(e,{zIndex:a-1})},[t.canvasData,u,h]),N=(0,l.useCallback)(()=>{s({type:"UNDO"})},[]),w=(0,l.useCallback)(()=>{s({type:"REDO"})},[]),C=(0,l.useCallback)(()=>{s({type:"CLEAR_CANVAS"})},[]),k=(0,l.useCallback)(e=>{s({type:"LOAD_CANVAS_DATA",payload:e})},[]),A={...t,setTool:r,selectObject:n,addTextLayer:o,addObjectLayer:m,updateTextLayer:u,updateObjectLayer:h,deleteLayer:x,setBackground:p,moveLayer:f,resizeLayer:g,rotateLayer:j,setLayerOpacity:v,bringToFront:y,sendToBack:b,undo:N,redo:w,clearCanvas:C,loadCanvasData:k};return(0,l.createElement)(d.Provider,{value:A},a)}function m(){let e=(0,l.useContext)(d);if(void 0===e)throw Error("useEditor must be used within an EditorProvider");return e}var u=t(7579);function h(e){let{width:a=800,height:t=600}=e,{canvasData:r,selectedObjectId:n,selectObject:i,moveLayer:c,resizeLayer:d,rotateLayer:o,tool:h}=m(),x=(0,l.useRef)(null),p=(0,l.useRef)(null),[f,g]=(0,l.useState)({});(0,l.useEffect)(()=>{(async()=>{var e;let a=[];if(r.objects.forEach(e=>{f[e.id]||a.push(new Promise((a,t)=>{let s=new window.Image;s.crossOrigin="anonymous",s.onload=()=>a({id:e.id,image:s}),s.onerror=t,s.src=e.src}))}),(null==(e=r.background)?void 0:e.type)!=="image"||f.background||a.push(new Promise((e,a)=>{let t=new window.Image;t.crossOrigin="anonymous",t.onload=()=>e({id:"background",image:t}),t.onerror=a,t.src=r.background.value})),a.length>0)try{let e=(await Promise.all(a)).reduce((e,a)=>{let{id:t,image:s}=a;return e[t]=s,e},{});g(a=>({...a,...e}))}catch(e){console.error("Error loading images:",e)}})()},[r.objects,r.background,f]),(0,l.useEffect)(()=>{var e,a;if(p.current&&n){let a=x.current;if(a){let t=a.findOne("#".concat(n));t&&(p.current.nodes([t]),null==(e=p.current.getLayer())||e.batchDraw())}}else p.current&&(p.current.nodes([]),null==(a=p.current.getLayer())||a.batchDraw())},[n]);let j=e=>{if(e.target===e.target.getStage())return void i(null);let a=e.target.id();a&&"select"===h&&i(a)},v=(e,a)=>{c(a,e.target.x(),e.target.y())},y=(e,a)=>{let t=e.target,s=t.scaleX(),l=t.scaleY();t.scaleX(1),t.scaleY(1),d(a,Math.max(5,t.width()*s),Math.max(5,t.height()*l)),o(a,t.rotation()),c(a,t.x(),t.y())};return(0,s.jsx)("div",{className:"border border-gray-300 bg-white",children:(0,s.jsxs)(u.BI,{ref:x,width:a,height:t,onClick:j,onTap:j,children:[(0,s.jsxs)(u.Wd,{children:[r.background?"color"===r.background.type?(0,s.jsx)(u.rw,{width:a,height:t,fill:r.background.value}):"gradient"===r.background.type?(0,s.jsx)(u.rw,{width:a,height:t,fillLinearGradientStartPoint:{x:0,y:0},fillLinearGradientEndPoint:{x:a,y:t},fillLinearGradientColorStops:[0,"#667eea",1,"#764ba2"]}):"image"===r.background.type&&f.background?(0,s.jsx)(u._V,{image:f.background,width:a,height:t,x:0,y:0}):null:null,r.objects.sort((e,a)=>e.zIndex-a.zIndex).map(e=>{let a=f[e.id];return a?(0,s.jsx)(u._V,{id:e.id,image:a,x:e.x,y:e.y,width:e.width,height:e.height,rotation:e.rotation,opacity:e.opacity,scaleX:e.flipX?-1:1,scaleY:e.flipY?-1:1,draggable:"select"===h,onDragEnd:a=>v(a,e.id),onTransformEnd:a=>y(a,e.id),onClick:()=>"select"===h&&i(e.id)},e.id):null}),r.texts.sort((e,a)=>e.zIndex-a.zIndex).map(e=>{var a,t,l,r;return(0,s.jsx)(u.EY,{id:e.id,text:e.content,x:e.x,y:e.y,width:e.width,height:e.height,fontSize:e.fontSize,fontFamily:e.fontFamily,fontStyle:e.fontWeight,fill:e.color,align:e.textAlign,rotation:e.rotation,opacity:e.opacity,stroke:e.stroke,strokeWidth:e.strokeWidth,shadowColor:null==(a=e.shadow)?void 0:a.color,shadowBlur:null==(t=e.shadow)?void 0:t.blur,shadowOffsetX:null==(l=e.shadow)?void 0:l.offsetX,shadowOffsetY:null==(r=e.shadow)?void 0:r.offsetY,draggable:"select"===h,onDragEnd:a=>v(a,e.id),onTransformEnd:a=>y(a,e.id),onClick:()=>"select"===h&&i(e.id)},e.id)})]}),(0,s.jsx)(u.Wd,{children:(0,s.jsx)(u.Ge,{ref:p,boundBoxFunc:(e,a)=>a.width<5||a.height<5?e:a})})]})})}var x=t(285),p=t(7606),f=t(2523),g=t(5057);let j=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...l})});j.displayName="Textarea";var v=t(4165);let y=(0,t(2085).F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function b(e){let{className:a,variant:t,...l}=e;return(0,s.jsx)("div",{className:(0,r.cn)(y({variant:t}),a),...l})}var N=t(2099);class w{async saveTemplate(e,a,t,s,l,r){let{data:{user:n}}=await this.supabase.auth.getUser();if(!n)throw Error("User not authenticated");let i={title:e,description:a,canvas_data:t,category:s,tags:l,preview_url:r,author_id:n.id,is_public:!1,status:"draft"},{data:c,error:d}=await this.supabase.from("templates").insert(i).select().single();if(d)throw d;return c}async updateTemplate(e,a){let{data:t,error:s}=await this.supabase.from("templates").update(a).eq("id",e).select().single();if(s)throw s;return t}async deleteTemplate(e){let{error:a}=await this.supabase.from("templates").delete().eq("id",e);if(a)throw a}async getPublicTemplates(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:12,t=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"created_at",r=(e-1)*a,n=this.supabase.from("templates").select("\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      ",{count:"exact"}).eq("is_public",!0).eq("status","approved").range(r,r+a-1);switch(t&&(n=n.contains("category",[t])),s&&s.length>0&&(n=n.overlaps("tags",s)),l){case"popular":n=n.order("usage_count",{ascending:!1});break;case"rating":n=n.order("rating",{ascending:!1});break;default:n=n.order("created_at",{ascending:!1})}let{data:i,error:c,count:d}=await n;if(c)throw c;return{templates:i,total:d||0}}async getUserTemplates(e){let a=e;if(!a){let{data:{user:e}}=await this.supabase.auth.getUser();if(!e)throw Error("User not authenticated");a=e.id}let{data:t,error:s}=await this.supabase.from("templates").select("*").eq("author_id",a).order("created_at",{ascending:!1});if(s)throw s;return t}async getTemplate(e){let{data:a,error:t}=await this.supabase.from("templates").select("\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      ").eq("id",e).single();if(t)throw t;return a}async duplicateTemplate(e){let a=await this.getTemplate(e),{data:{user:t}}=await this.supabase.auth.getUser();if(!t)throw Error("User not authenticated");let s={title:"".concat(a.title," (副本)"),description:a.description,canvas_data:a.data,category:a.category,tags:a.tags,author_id:t.id,is_public:!1,status:"draft"},{data:l,error:r}=await this.supabase.from("templates").insert(s).select().single();if(r)throw r;return l}async incrementUsage(e){let{error:a}=await this.supabase.rpc("increment_template_usage",{template_uuid:e});if(a)throw a}async searchTemplates(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:12,s=(a-1)*t,{data:l,error:r,count:n}=await this.supabase.from("templates").select("\n        *,\n        profiles:author_id (\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      ",{count:"exact"}).eq("is_public",!0).eq("status","approved").or("title.ilike.%".concat(e,"%,description.ilike.%").concat(e,"%")).range(s,s+t-1).order("created_at",{ascending:!1});if(r)throw r;return{templates:l,total:n||0}}async getPopularTemplates(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,{data:a,error:t}=await this.supabase.rpc("get_popular_templates",{limit_count:e});if(t)throw t;return a}async getCategories(){let{data:e,error:a}=await this.supabase.from("templates").select("category").eq("is_public",!0).eq("status","approved");if(a)throw a;let t={};return e.forEach(e=>{e.category.forEach(e=>{t[e]=(t[e]||0)+1})}),Object.entries(t).map(e=>{let[a,t]=e;return{category:a,count:t}})}async getTags(){let{data:e,error:a}=await this.supabase.from("templates").select("tags").eq("is_public",!0).eq("status","approved");if(a)throw a;let t={};return e.forEach(e=>{e.tags.forEach(e=>{t[e]=(t[e]||0)+1})}),Object.entries(t).map(e=>{let[a,t]=e;return{tag:a,count:t}})}constructor(){this.supabase=(0,N.i)()}}let C=new w;var k=t(4229),A=t(4416);let S=["早安","午安","晚安","勵志","關心","祝福","節慶","生日","感謝","道歉","加油","問候"],E=["溫馨","可愛","正能量","勵志","感動","幽默","清新","優雅","活潑","溫暖","美好","希望"];function _(e){var a;let{open:t,onOpenChange:r,onSaved:n}=e,{canvasData:i}=m(),{user:c}=(0,p.A)(),[d,o]=(0,l.useState)(""),[u,h]=(0,l.useState)(""),[y,N]=(0,l.useState)([]),[w,_]=(0,l.useState)([]),[I,z]=(0,l.useState)(""),[R,T]=(0,l.useState)(!1),[D,L]=(0,l.useState)(!1),J=async()=>{if(!d.trim())return void alert("請輸入樣板標題");if(!c)return void alert("請先登入");L(!0);try{let e=await C.saveTemplate(d,u,i,y,w,"https://via.placeholder.com/400x300");null==n||n(e.id),r(!1),o(""),h(""),N([]),_([]),z(""),T(!1)}catch(e){console.error("Failed to save template:",e),alert("儲存失敗，請稍後再試")}finally{L(!1)}},P=()=>{I.trim()&&!w.includes(I.trim())&&(_(e=>[...e,I.trim()]),z(""))};return(0,s.jsx)(v.lG,{open:t,onOpenChange:r,children:(0,s.jsxs)(v.Cf,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,s.jsx)(v.c7,{children:(0,s.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-5 w-5"}),"儲存為樣板"]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"title",children:"樣板標題 *"}),(0,s.jsx)(f.p,{id:"title",value:d,onChange:e=>o(e.target.value),placeholder:"為您的樣板取個好名字",className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{htmlFor:"description",children:"樣板描述"}),(0,s.jsx)(j,{id:"description",value:u,onChange:e=>h(e.target.value),placeholder:"簡單描述這個樣板的特色和用途",className:"mt-1",rows:3})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"分類標籤"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:S.map(e=>(0,s.jsx)(b,{variant:y.includes(e)?"default":"outline",className:"cursor-pointer",onClick:()=>{N(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},children:e},e))}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"選擇適合的分類，幫助其他人找到您的樣板"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"風格標籤"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:E.map(e=>(0,s.jsx)(b,{variant:w.includes(e)?"default":"outline",className:"cursor-pointer",onClick:()=>{_(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},children:e},e))}),(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsx)(f.p,{value:I,onChange:e=>z(e.target.value),placeholder:"新增自訂標籤",onKeyPress:e=>"Enter"===e.key&&P(),className:"flex-1"}),(0,s.jsx)(x.$,{variant:"outline",onClick:P,disabled:!I.trim(),children:"新增"})]}),w.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:w.map(e=>(0,s.jsxs)(b,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,s.jsx)(A.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>{_(a=>a.filter(a=>a!==e))}})]},e))})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:"isPublic",checked:R,onChange:e=>T(e.target.checked),className:"rounded"}),(0,s.jsx)(g.J,{htmlFor:"isPublic",className:"text-sm",children:"公開分享（其他使用者可以看到並使用這個樣板）"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"樣板預覽"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("p",{children:["畫布尺寸: ",i.width," x ",i.height]}),(0,s.jsxs)("p",{children:["文字圖層: ",i.texts.length," 個"]}),(0,s.jsxs)("p",{children:["物件圖層: ",i.objects.length," 個"]}),(0,s.jsxs)("p",{children:["背景: ",(null==(a=i.background)?void 0:a.type)||"無"]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>r(!1),disabled:D,children:"取消"}),(0,s.jsx)(x.$,{onClick:J,disabled:D||!d.trim(),children:D?"儲存中...":"儲存樣板"})]})]})]})})}var I=t(2918),z=t(6474),R=t(7863),T=t(5196);let D=I.bL;I.YJ;let L=I.WT,J=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(I.l9,{ref:a,className:(0,r.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[l,(0,s.jsx)(I.In,{asChild:!0,children:(0,s.jsx)(z.A,{className:"h-4 w-4 opacity-50"})})]})});J.displayName=I.l9.displayName;let P=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(I.PP,{ref:a,className:(0,r.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(R.A,{className:"h-4 w-4"})})});P.displayName=I.PP.displayName;let O=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(I.wn,{ref:a,className:(0,r.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(z.A,{className:"h-4 w-4"})})});O.displayName=I.wn.displayName;let $=l.forwardRef((e,a)=>{let{className:t,children:l,position:n="popper",...i}=e;return(0,s.jsx)(I.ZL,{children:(0,s.jsxs)(I.UC,{ref:a,className:(0,r.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(P,{}),(0,s.jsx)(I.LM,{className:(0,r.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(O,{})]})})});$.displayName=I.UC.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(I.JU,{ref:a,className:(0,r.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=I.JU.displayName;let F=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(I.q7,{ref:a,className:(0,r.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(I.VF,{children:(0,s.jsx)(T.A,{className:"h-4 w-4"})})}),(0,s.jsx)(I.p4,{children:l})]})});F.displayName=I.q7.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(I.wv,{ref:a,className:(0,r.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=I.wv.displayName;var U=t(4073);let B=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsxs)(U.bL,{ref:a,className:(0,r.cn)("relative flex w-full touch-none select-none items-center",t),...l,children:[(0,s.jsx)(U.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,s.jsx)(U.Q6,{className:"absolute h-full bg-primary"})}),(0,s.jsx)(U.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});B.displayName=U.bL.displayName;var W=t(704);let Z=W.bL,Y=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(W.B8,{ref:a,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...l})});Y.displayName=W.B8.displayName;let q=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(W.l9,{ref:a,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...l})});q.displayName=W.l9.displayName;let V=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(W.UC,{ref:a,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...l})});V.displayName=W.UC.displayName;var M=t(5003),G=t.n(M);class X{static async exportCanvas(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{format:"png"},{format:t="png",quality:s=.9,width:l=e.width,height:r=e.height,pixelRatio:n=2}=a,i=new(G()).Stage({container:document.createElement("div"),width:l,height:r}),c=new(G()).Layer;i.add(c);try{for(let a of(await this.renderBackground(c,e,l,r),[...e.objects].sort((e,a)=>e.zIndex-a.zIndex)))await this.renderObject(c,a);for(let a of[...e.texts].sort((e,a)=>e.zIndex-a.zIndex))this.renderText(c,a);return i.toDataURL({mimeType:"png"===t?"image/png":"image/jpeg",quality:"jpeg"===t?s:void 0,pixelRatio:n})}finally{i.destroy()}}static async renderBackground(e,a,t,s){if(a.background){if("color"===a.background.type){let l=new(G()).Rect({x:0,y:0,width:t,height:s,fill:a.background.value});e.add(l)}else if("image"===a.background.type){let l=await this.loadImage(a.background.value),r=new(G()).Image({x:0,y:0,width:t,height:s,image:l});e.add(r)}else if("gradient"===a.background.type){let a=new(G()).Rect({x:0,y:0,width:t,height:s,fillLinearGradientStartPoint:{x:0,y:0},fillLinearGradientEndPoint:{x:t,y:s},fillLinearGradientColorStops:[0,"#667eea",1,"#764ba2"]});e.add(a)}}}static async renderObject(e,a){let t=await this.loadImage(a.src),s=new(G()).Image({x:a.x,y:a.y,width:a.width,height:a.height,rotation:a.rotation,opacity:a.opacity,scaleX:a.flipX?-1:1,scaleY:a.flipY?-1:1,image:t});e.add(s)}static renderText(e,a){var t,s,l,r;let n=new(G()).Text({x:a.x,y:a.y,width:a.width,height:a.height,text:a.content,fontSize:a.fontSize,fontFamily:a.fontFamily,fontStyle:a.fontWeight,fill:a.color,align:a.textAlign,rotation:a.rotation,opacity:a.opacity,stroke:a.stroke,strokeWidth:a.strokeWidth,shadowColor:null==(t=a.shadow)?void 0:t.color,shadowBlur:null==(s=a.shadow)?void 0:s.blur,shadowOffsetX:null==(l=a.shadow)?void 0:l.offsetX,shadowOffsetY:null==(r=a.shadow)?void 0:r.offsetY});e.add(n)}static loadImage(e){return new Promise((a,t)=>{let s=new Image;s.crossOrigin="anonymous",s.onload=()=>a(s),s.onerror=t,s.src=e})}static downloadImage(e,a){let t=document.createElement("a");t.download=a,t.href=e,document.body.appendChild(t),t.click(),document.body.removeChild(t)}static async copyToClipboard(e){try{let a=await fetch(e),t=await a.blob();await navigator.clipboard.write([new ClipboardItem({[t.type]:t})])}catch(e){throw console.error("Failed to copy to clipboard:",e),e}}static shareToSocial(e,a,t){let s=encodeURIComponent(t||"來看看我用早安圖產生器製作的圖片！"),l=encodeURIComponent(window.location.origin),r="";switch(e){case"facebook":r="https://www.facebook.com/sharer/sharer.php?u=".concat(l,"&quote=").concat(s);break;case"twitter":r="https://twitter.com/intent/tweet?text=".concat(s,"&url=").concat(l);break;case"line":r="https://social-plugins.line.me/lineit/share?url=".concat(l,"&text=").concat(s)}r&&window.open(r,"_blank","width=600,height=400")}static generateShareLink(e){let a=window.location.origin;return e?"".concat(a,"/template/").concat(e):a}static previewImage(e){let a=window.open();a&&a.document.write('\n        <html>\n          <head>\n            <title>圖片預覽</title>\n            <style>\n              body {\n                margin: 0;\n                padding: 20px;\n                background: #f0f0f0;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                min-height: 100vh;\n              }\n              img {\n                max-width: 100%;\n                max-height: 100%;\n                box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n                border-radius: 8px;\n              }\n            </style>\n          </head>\n          <body>\n            <img src="'.concat(e,'" alt="Generated Image" />\n          </body>\n        </html>\n      '))}static getImageInfo(e){return new Promise((a,t)=>{let s=new Image;s.onload=()=>{let t=e.split(",")[1].length,l=e.split(";")[0].split("/")[1];a({width:s.width,height:s.height,size:3*t/4,format:l})},s.onerror=t,s.src=e})}}var K=t(1788),H=t(4357),Q=t(2657),ee=t(488),ea=t(556),et=t(1366),es=t(8164);let el=[{name:"原始尺寸",width:800,height:600},{name:"Instagram 正方形",width:1080,height:1080},{name:"Facebook 封面",width:1200,height:630},{name:"Twitter 標頭",width:1500,height:500},{name:"LINE 貼圖",width:370,height:320},{name:"手機桌布",width:1080,height:1920}];function er(e){var a;let{open:t,onOpenChange:r}=e,{canvasData:n}=m(),[i,c]=(0,l.useState)("png"),[d,o]=(0,l.useState)(90),[u,h]=(0,l.useState)(800),[p,f]=(0,l.useState)(600),[j,y]=(0,l.useState)(2),[b,N]=(0,l.useState)(!1),[w,C]=(0,l.useState)(null),[k,A]=(0,l.useState)(null);(0,l.useEffect)(()=>{t&&(h(n.width),f(n.height),C(null),A(null))},[t,n.width,n.height]);let S=async()=>{N(!0);try{let e=await X.exportCanvas(n,{format:i,quality:"jpeg"===i?d/100:void 0,width:u,height:p,pixelRatio:j});C(e);let a=await X.getImageInfo(e);A(a)}catch(e){console.error("Export failed:",e),alert("匯出失敗，請稍後再試")}finally{N(!1)}},E=async()=>{if(w)try{await X.copyToClipboard(w),alert("已複製到剪貼簿")}catch(e){alert("複製失敗，請使用下載功能")}},_=e=>{X.shareToSocial(e,w||void 0,"來看看我用早安圖產生器製作的圖片！")};return(0,s.jsx)(v.lG,{open:t,onOpenChange:r,children:(0,s.jsxs)(v.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)(v.c7,{children:(0,s.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(K.A,{className:"h-5 w-5"}),"匯出與分享"]})}),(0,s.jsxs)(Z,{defaultValue:"export",className:"w-full",children:[(0,s.jsxs)(Y,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(q,{value:"export",children:"匯出設定"}),(0,s.jsx)(q,{value:"share",children:"分享"})]}),(0,s.jsx)(V,{value:"export",className:"space-y-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-medium",children:"匯出設定"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"檔案格式"}),(0,s.jsxs)(D,{value:i,onValueChange:e=>c(e),children:[(0,s.jsx)(J,{className:"mt-1",children:(0,s.jsx)(L,{})}),(0,s.jsxs)($,{children:[(0,s.jsx)(F,{value:"png",children:"PNG (透明背景)"}),(0,s.jsx)(F,{value:"jpeg",children:"JPEG (較小檔案)"})]})]})]}),"jpeg"===i&&(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{children:["品質: ",d,"%"]}),(0,s.jsx)(B,{value:[d],onValueChange:e=>{let[a]=e;return o(a)},min:10,max:100,step:5,className:"mt-1"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"寬度 (px)"}),(0,s.jsx)("input",{type:"number",value:u,onChange:e=>h(parseInt(e.target.value)||800),className:"w-full mt-1 px-3 py-2 border rounded-md",min:"100",max:"4000"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"高度 (px)"}),(0,s.jsx)("input",{type:"number",value:p,onChange:e=>f(parseInt(e.target.value)||600),className:"w-full mt-1 px-3 py-2 border rounded-md",min:"100",max:"4000"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{children:"快速尺寸"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-1",children:el.map(e=>(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{h(e.width),f(e.height)},className:"text-xs",children:e.name},e.name))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{children:["解析度倍數: ",j,"x"]}),(0,s.jsx)(B,{value:[j],onValueChange:e=>{let[a]=e;return y(a)},min:1,max:4,step:.5,className:"mt-1"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"更高的倍數會產生更清晰但更大的檔案"})]}),(0,s.jsx)(x.$,{onClick:S,disabled:b,className:"w-full",children:b?"匯出中...":"生成圖片"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-medium",children:"預覽"}),w?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,s.jsx)("img",{src:w,alt:"Exported",className:"w-full h-auto max-h-64 object-contain bg-gray-50"})}),k&&(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsxs)("p",{children:["尺寸: ",k.width," x ",k.height]}),(0,s.jsxs)("p",{children:["格式: ",k.format.toUpperCase()]}),(0,s.jsxs)("p",{children:["檔案大小: ",(a=k.size)<1024?"".concat(a," B"):a<1048576?"".concat((a/1024).toFixed(1)," KB"):"".concat((a/1048576).toFixed(1)," MB")]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(x.$,{onClick:()=>{if(w){let e="meme-".concat(Date.now(),".").concat(i);X.downloadImage(w,e)}},className:"flex-1",children:[(0,s.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"下載"]}),(0,s.jsx)(x.$,{variant:"outline",onClick:E,children:(0,s.jsx)(H.A,{className:"h-4 w-4"})}),(0,s.jsx)(x.$,{variant:"outline",onClick:()=>{w&&X.previewImage(w)},children:(0,s.jsx)(Q.A,{className:"h-4 w-4"})})]})]}):(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500",children:"點擊「生成圖片」來預覽結果"})]})]})}),(0,s.jsx)(V,{value:"share",className:"space-y-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-medium",children:"分享到社群媒體"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>_("facebook"),className:"flex items-center gap-2",children:[(0,s.jsx)(ee.A,{className:"h-4 w-4 text-blue-600"}),"Facebook"]}),(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>_("twitter"),className:"flex items-center gap-2",children:[(0,s.jsx)(ea.A,{className:"h-4 w-4 text-blue-400"}),"Twitter"]}),(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>_("line"),className:"flex items-center gap-2",children:[(0,s.jsx)(et.A,{className:"h-4 w-4 text-green-500"}),"LINE"]})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"分享連結"}),(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>{let e=X.generateShareLink();navigator.clipboard.writeText(e),alert("分享連結已複製到剪貼簿")},className:"flex items-center gap-2",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),"複製分享連結"]})]})]})})]})]})})}var en=t(2103),ei=t(3500),ec=t(7213),ed=t(3127),eo=t(3654),em=t(8932),eu=t(2525),eh=t(5040);function ex(e){let{onSave:a,onExport:t,onOpenTemplateLibrary:n}=e,{tool:i,setTool:c,selectedObjectId:d,deleteLayer:o,undo:u,redo:h,clearCanvas:p,history:f,historyIndex:g}=m(),[j,v]=(0,l.useState)(!1),[y,b]=(0,l.useState)(!1),N=[{id:"select",icon:en.A,label:"選擇"},{id:"text",icon:ei.A,label:"文字"},{id:"object",icon:ec.A,label:"物件"},{id:"background",icon:ed.A,label:"背景"}],w=g<f.length-1;return(0,s.jsxs)("div",{className:"flex items-center gap-2 p-4 bg-white border-b border-gray-200",children:[(0,s.jsx)("div",{className:"flex items-center gap-1 mr-4",children:N.map(e=>{let a=e.icon;return(0,s.jsxs)(x.$,{variant:i===e.id?"default":"ghost",size:"sm",onClick:()=>c(e.id),className:(0,r.cn)("flex items-center gap-2",i===e.id&&"bg-blue-100 text-blue-700"),children:[(0,s.jsx)(a,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})}),(0,s.jsx)("div",{className:"w-px h-6 bg-gray-300 mr-4"}),(0,s.jsxs)("div",{className:"flex items-center gap-1 mr-4",children:[(0,s.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:u,disabled:!(g>0),title:"復原 (Ctrl+Z)",children:[(0,s.jsx)(eo.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline ml-2",children:"復原"})]}),(0,s.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:h,disabled:!w,title:"重做 (Ctrl+Y)",children:[(0,s.jsx)(em.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline ml-2",children:"重做"})]})]}),(0,s.jsx)("div",{className:"w-px h-6 bg-gray-300 mr-4"}),d&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:()=>o(d),className:"text-red-600 hover:text-red-700 hover:bg-red-50",title:"刪除選中物件",children:[(0,s.jsx)(eu.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline ml-2",children:"刪除"})]}),(0,s.jsx)("div",{className:"w-px h-6 bg-gray-300 mr-4"})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 mr-4",children:(0,s.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:p,className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",title:"清空畫布",children:[(0,s.jsx)(eu.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline ml-2",children:"清空"})]})}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:n,className:"flex items-center gap-2",children:[(0,s.jsx)(eh.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"樣板庫"})]}),(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>v(!0),className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"存為樣板"})]}),a&&(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:a,className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"儲存"})]}),(0,s.jsxs)(x.$,{size:"sm",onClick:()=>b(!0),className:"flex items-center gap-2",children:[(0,s.jsx)(K.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"匯出"})]}),t&&(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:t,className:"flex items-center gap-2",children:[(0,s.jsx)(K.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"快速匯出"})]})]}),(0,s.jsx)(_,{open:j,onOpenChange:v,onSaved:e=>{console.log("Template saved:",e)}}),(0,s.jsx)(er,{open:y,onOpenChange:b})]})}var ep=t(6695),ef=t(8749);function eg(){let{canvasData:e,selectedObjectId:a,selectObject:t,deleteLayer:l,bringToFront:n,sendToBack:i,setLayerOpacity:c,updateTextLayer:d,updateObjectLayer:o}=m(),u=[...e.texts.map(e=>({...e,layerType:"text"})),...e.objects.map(e=>({...e,layerType:"object"}))].sort((e,a)=>a.zIndex-e.zIndex);return 0===u.length?(0,s.jsxs)(ep.Zp,{className:"w-64",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsx)(ep.ZB,{className:"text-sm",children:"圖層"})}),(0,s.jsx)(ep.Wu,{children:(0,s.jsx)("p",{className:"text-sm text-gray-500 text-center py-8",children:"尚無圖層"})})]}):(0,s.jsxs)(ep.Zp,{className:"w-64",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsx)(ep.ZB,{className:"text-sm",children:"圖層"})}),(0,s.jsxs)(ep.Wu,{className:"p-2",children:[(0,s.jsx)("div",{className:"space-y-1",children:u.map(e=>{let c="text"===e.layerType?ei.A:ec.A,m=a===e.id,u=e.opacity>0;return(0,s.jsxs)("div",{className:(0,r.cn)("flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50",m&&"bg-blue-50 border border-blue-200"),onClick:()=>t(e.id),children:[(0,s.jsx)(c,{className:"h-4 w-4 text-gray-600"}),(0,s.jsx)("span",{className:"flex-1 text-sm truncate",children:"text"===e.layerType?e.content.length>20?"".concat(e.content.substring(0,20),"..."):e.content||"文字圖層":"圖片物件"}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:a=>{a.stopPropagation(),((e,a,t)=>{let s=t>0?0:1;"text"===a?d(e,{opacity:s}):o(e,{opacity:s})})(e.id,e.layerType,e.opacity)},title:u?"隱藏圖層":"顯示圖層",children:u?(0,s.jsx)(Q.A,{className:"h-3 w-3"}):(0,s.jsx)(ef.A,{className:"h-3 w-3 text-gray-400"})}),(0,s.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:a=>{a.stopPropagation(),n(e.id)},title:"移到最上層",children:(0,s.jsx)(R.A,{className:"h-3 w-3"})}),(0,s.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:a=>{a.stopPropagation(),i(e.id)},title:"移到最下層",children:(0,s.jsx)(z.A,{className:"h-3 w-3"})}),(0,s.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:a=>{a.stopPropagation(),l(e.id)},title:"刪除圖層",children:(0,s.jsx)(eu.A,{className:"h-3 w-3"})})]})]},e.id)})}),(0,s.jsx)("div",{className:"mt-4 pt-2 border-t border-gray-200",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["共 ",u.length," 個圖層"]})})]})]})}var ej=t(7325),ev=t(7733),ey=t(8702);let eb=[{value:"Arial",label:"Arial"},{value:"Helvetica",label:"Helvetica"},{value:"Times New Roman",label:"Times New Roman"},{value:"Georgia",label:"Georgia"},{value:"Verdana",label:"Verdana"},{value:"Courier New",label:"Courier New"},{value:"Impact",label:"Impact"},{value:"Comic Sans MS",label:"Comic Sans MS"}],eN=[{value:"normal",label:"正常"},{value:"bold",label:"粗體"},{value:"100",label:"極細"},{value:"300",label:"細"},{value:"500",label:"中等"},{value:"700",label:"粗"},{value:"900",label:"極粗"}],ew=[{value:"left",icon:ej.A,label:"靠左對齊"},{value:"center",icon:ev.A,label:"置中對齊"},{value:"right",icon:ey.A,label:"靠右對齊"}];function eC(){let{tool:e,canvasData:a,selectedObjectId:t,addTextLayer:r,updateTextLayer:n}=m(),[i,c]=(0,l.useState)("輸入文字"),d=a.texts.find(e=>e.id===t),o=()=>{i.trim()&&(r(i,100,100),c("輸入文字"))},u=(e,a)=>{d&&n(d.id,{[e]:a})};return"text"!==e?null:(0,s.jsxs)(ep.Zp,{className:"w-80",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsxs)(ep.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ei.A,{className:"h-4 w-4"}),"文字工具"]})}),(0,s.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"新增文字"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(f.p,{value:i,onChange:e=>c(e.target.value),placeholder:"輸入文字內容",onKeyPress:e=>"Enter"===e.key&&o()}),(0,s.jsx)(x.$,{onClick:o,size:"sm",children:"新增"})]})]}),d&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-3",children:"文字屬性"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"文字內容"}),(0,s.jsx)(f.p,{value:d.content,onChange:e=>u("content",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"字型"}),(0,s.jsxs)(D,{value:d.fontFamily,onValueChange:e=>u("fontFamily",e),children:[(0,s.jsx)(J,{children:(0,s.jsx)(L,{})}),(0,s.jsx)($,{children:eb.map(e=>(0,s.jsx)(F,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(g.J,{children:["字體大小: ",d.fontSize,"px"]}),(0,s.jsx)(B,{value:[d.fontSize],onValueChange:e=>{let[a]=e;return u("fontSize",a)},min:8,max:200,step:1})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"字重"}),(0,s.jsxs)(D,{value:d.fontWeight,onValueChange:e=>u("fontWeight",e),children:[(0,s.jsx)(J,{children:(0,s.jsx)(L,{})}),(0,s.jsx)($,{children:eN.map(e=>(0,s.jsx)(F,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"文字顏色"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"color",value:d.color,onChange:e=>{u("color",e.target.value)},className:"w-12 h-8 rounded border border-gray-300 cursor-pointer"}),(0,s.jsx)(f.p,{value:d.color,onChange:e=>u("color",e.target.value),className:"flex-1"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"對齊方式"}),(0,s.jsx)("div",{className:"flex gap-1",children:ew.map(e=>{let a=e.icon;return(0,s.jsx)(x.$,{variant:d.textAlign===e.value?"default":"outline",size:"sm",onClick:()=>u("textAlign",e.value),title:e.label,children:(0,s.jsx)(a,{className:"h-4 w-4"})},e.value)})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(g.J,{children:["透明度: ",Math.round(100*d.opacity),"%"]}),(0,s.jsx)(B,{value:[d.opacity],onValueChange:e=>{let[a]=e;return u("opacity",a)},min:0,max:1,step:.01})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{children:"描邊"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"color",value:d.stroke||"#000000",onChange:e=>u("stroke",e.target.value),className:"w-12 h-8 rounded border border-gray-300 cursor-pointer"}),(0,s.jsx)(f.p,{type:"number",value:d.strokeWidth||0,onChange:e=>u("strokeWidth",parseInt(e.target.value)||0),placeholder:"寬度",min:"0",max:"20"})]})]})]})}),!d&&(0,s.jsxs)("div",{className:"text-sm text-gray-500 bg-gray-50 p-3 rounded",children:[(0,s.jsx)("p",{children:"• 點擊「新增」按鈕來新增文字"}),(0,s.jsx)("p",{children:"• 選擇文字圖層來編輯屬性"}),(0,s.jsx)("p",{children:"• 在畫布上拖拽來移動文字"})]})]})]})}let ek=[{name:"標題",style:{fontSize:48,fontFamily:"Arial",fontWeight:"bold",color:"#000000",textAlign:"center"}},{name:"副標題",style:{fontSize:32,fontFamily:"Arial",fontWeight:"normal",color:"#333333",textAlign:"center"}},{name:"內文",style:{fontSize:24,fontFamily:"Arial",fontWeight:"normal",color:"#666666",textAlign:"left"}},{name:"早安問候",style:{fontSize:36,fontFamily:"Georgia",fontWeight:"bold",color:"#ff6b6b",textAlign:"center",stroke:"#ffffff",strokeWidth:2}},{name:"溫馨祝福",style:{fontSize:28,fontFamily:"Comic Sans MS",fontWeight:"normal",color:"#4ecdc4",textAlign:"center"}},{name:"勵志標語",style:{fontSize:32,fontFamily:"Impact",fontWeight:"bold",color:"#45b7d1",textAlign:"center",stroke:"#ffffff",strokeWidth:1}}],eA=["早安！美好的一天開始了","願你今天充滿陽光與快樂","新的一天，新的希望","早安！願你笑容滿面","今天也要加油喔！","美好的早晨，美好的心情","早安！祝你一切順利","陽光正好，微風不燥","今天是充滿可能的一天","早安！願你被溫柔以待"];function eS(){let{addTextLayer:e,selectedObjectId:a,updateTextLayer:t,canvasData:l}=m(),r=l.texts.find(e=>e.id===a);return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(ep.Zp,{children:[(0,s.jsx)(ep.aR,{children:(0,s.jsx)(ep.ZB,{className:"text-sm",children:"文字樣式預設"})}),(0,s.jsxs)(ep.Wu,{children:[(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:ek.map(e=>(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>r?void(r&&t(r.id,e.style)):null,disabled:!r,className:"h-auto p-2 text-left",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-xs",children:e.name}),(0,s.jsx)("div",{className:"text-xs mt-1",style:{fontSize:Math.min(e.style.fontSize/3,12),fontFamily:e.style.fontFamily,fontWeight:e.style.fontWeight,color:e.style.color},children:"範例文字"})]})},e.name))}),!r&&(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"選擇文字圖層來套用樣式預設"})]})]}),(0,s.jsxs)(ep.Zp,{children:[(0,s.jsx)(ep.aR,{children:(0,s.jsx)(ep.ZB,{className:"text-sm",children:"問候語建議"})}),(0,s.jsx)(ep.Wu,{children:(0,s.jsx)("div",{className:"space-y-2",children:eA.map((a,r)=>(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"flex-1 text-sm",children:a}),(0,s.jsx)("div",{className:"flex gap-1",children:ek.slice(3,6).map(r=>(0,s.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>{e(a,100,100),setTimeout(()=>{let e=l.texts[l.texts.length-1];e&&t(e.id,r.style)},0)},className:"px-2 py-1 h-auto text-xs",title:"使用".concat(r.name,"樣式"),children:r.name},r.name))})]},r))})})]})]})}var eE=t(9869),e_=t(3311);let eI=["#ffffff","#f8f9fa","#e9ecef","#dee2e6","#ced4da","#adb5bd","#6c757d","#495057","#343a40","#212529","#ff6b6b","#ee5a52","#ff8787","#ffa8a8","#ffc9c9","#51cf66","#40c057","#69db7c","#8ce99a","#b2f2bb","#339af0","#228be6","#74c0fc","#a5d8ff","#d0ebff","#845ef7","#7950f2","#9775fa","#b197fc","#d0bfff","#ffd43b","#fab005","#ffec99","#fff3bf","#fff9db","#fd7e14","#e8590c","#ffa94d","#ffb366","#ffc078"],ez=["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #f093fb 0%, #f5576c 100%)","linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)","linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)","linear-gradient(135deg, #fa709a 0%, #fee140 100%)","linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)","linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)"];function eR(){var e,a,t,n;let{tool:i,canvasData:c,setBackground:d}=m(),[o,u]=(0,l.useState)("#ffffff"),[h,p]=(0,l.useState)(""),[j,v]=(0,l.useState)(!1),y=(0,l.useRef)(null),b=async()=>{if(h.trim()){v(!0);try{setTimeout(()=>{let e=ez[Math.floor(Math.random()*ez.length)];d({type:"gradient",value:e}),v(!1)},2e3)}catch(e){console.error("AI generation failed:",e),v(!1)}}};return"background"!==i?null:(0,s.jsxs)(ep.Zp,{className:"w-80",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsxs)(ep.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-4 w-4"}),"背景設定"]})}),(0,s.jsxs)(ep.Wu,{children:[(0,s.jsxs)(Z,{defaultValue:"color",className:"w-full",children:[(0,s.jsxs)(Y,{className:"grid w-full grid-cols-4",children:[(0,s.jsx)(q,{value:"color",className:"text-xs",children:"顏色"}),(0,s.jsx)(q,{value:"gradient",className:"text-xs",children:"漸層"}),(0,s.jsx)(q,{value:"upload",className:"text-xs",children:"上傳"}),(0,s.jsx)(q,{value:"ai",className:"text-xs",children:"AI"})]}),(0,s.jsxs)(V,{value:"color",className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"預設顏色"}),(0,s.jsx)("div",{className:"grid grid-cols-5 gap-2 mt-2",children:eI.map(e=>{var a,t;return(0,s.jsx)("button",{className:(0,r.cn)("w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400 transition-colors",(null==(a=c.background)?void 0:a.type)==="color"&&(null==(t=c.background)?void 0:t.value)===e&&"border-blue-500 ring-2 ring-blue-200"),style:{backgroundColor:e},onClick:()=>{d({type:"color",value:e})},title:e},e)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"自訂顏色"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)("input",{type:"color",value:o,onChange:e=>{let a=e.target.value;u(a),d({type:"color",value:a})},className:"w-12 h-8 rounded border border-gray-300 cursor-pointer"}),(0,s.jsx)(f.p,{value:o,onChange:e=>{u(e.target.value),d({type:"color",value:e.target.value})},placeholder:"#ffffff",className:"flex-1"})]})]})]}),(0,s.jsx)(V,{value:"gradient",className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"預設漸層"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2 mt-2",children:ez.map((e,a)=>{var t,l;return(0,s.jsx)("button",{className:(0,r.cn)("w-full h-12 rounded border-2 border-gray-300 hover:border-gray-400 transition-colors",(null==(t=c.background)?void 0:t.type)==="gradient"&&(null==(l=c.background)?void 0:l.value)===e&&"border-blue-500 ring-2 ring-blue-200"),style:{background:e},onClick:()=>{d({type:"gradient",value:e})}},a)})})]})}),(0,s.jsxs)(V,{value:"upload",className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"上傳圖片"}),(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("input",{ref:y,type:"file",accept:"image/*",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){let e=new FileReader;e.onload=e=>{var a;d({type:"image",value:null==(a=e.target)?void 0:a.result})},e.readAsDataURL(t)}},className:"hidden"}),(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>{var e;return null==(e=y.current)?void 0:e.click()},className:"w-full flex items-center gap-2",children:[(0,s.jsx)(eE.A,{className:"h-4 w-4"}),"選擇圖片檔案"]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"支援 JPG, PNG 格式，建議尺寸 800x600 像素"})]}),(null==(e=c.background)?void 0:e.type)==="image"&&(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"目前背景"}),(0,s.jsx)("div",{className:"mt-2 border rounded overflow-hidden",children:(0,s.jsx)("img",{src:c.background.value,alt:"Background",className:"w-full h-24 object-cover"})})]})]}),(0,s.jsx)(V,{value:"ai",className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"AI 背景生成"}),(0,s.jsxs)("div",{className:"space-y-2 mt-2",children:[(0,s.jsx)(f.p,{value:h,onChange:e=>p(e.target.value),placeholder:"描述您想要的背景，例如：清晨的富士山、櫻花飛舞",onKeyPress:e=>"Enter"===e.key&&b()}),(0,s.jsxs)(x.$,{onClick:b,disabled:!h.trim()||j,className:"w-full flex items-center gap-2",children:[(0,s.jsx)(e_.A,{className:"h-4 w-4"}),j?"生成中...":"生成背景"]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"使用 AI 根據您的描述生成獨特的背景圖片"})]})})]}),(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["目前背景：",(null==(a=c.background)?void 0:a.type)==="color"&&"純色",(null==(t=c.background)?void 0:t.type)==="gradient"&&"漸層",(null==(n=c.background)?void 0:n.type)==="image"&&"圖片",!c.background&&"無"]})})]})]})}var eT=t(7924);let eD=[{id:"all",name:"全部",count:0},{id:"nature",name:"自然風景",count:0},{id:"abstract",name:"抽象圖案",count:0},{id:"texture",name:"材質紋理",count:0},{id:"gradient",name:"漸層背景",count:0},{id:"pattern",name:"圖案花紋",count:0},{id:"seasonal",name:"季節主題",count:0}],eL=[{id:"1",name:"清晨山景",url:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",category:"nature",tags:["山","清晨","自然"]},{id:"2",name:"櫻花背景",url:"https://images.unsplash.com/photo-1522383225653-ed111181a951?w=800&h=600&fit=crop",category:"nature",tags:["櫻花","春天","粉色"]},{id:"3",name:"抽象幾何",url:"https://images.unsplash.com/photo-1557683316-973673baf926?w=800&h=600&fit=crop",category:"abstract",tags:["幾何","抽象","現代"]},{id:"4",name:"木質紋理",url:"https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=800&h=600&fit=crop",category:"texture",tags:["木頭","紋理","自然"]},{id:"5",name:"海洋波浪",url:"https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=800&h=600&fit=crop",category:"nature",tags:["海洋","波浪","藍色"]},{id:"6",name:"花卉圖案",url:"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop",category:"pattern",tags:["花卉","圖案","裝飾"]}];function eJ(){let{setBackground:e}=m(),[a,t]=(0,l.useState)([]),[r,n]=(0,l.useState)([]),[i,c]=(0,l.useState)("all"),[d,o]=(0,l.useState)(""),[u,h]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{(async()=>{h(!0);try{let e=eL.map(e=>({...e,type:"background",is_public:!0,created_at:new Date().toISOString()}));t(e),n(e)}catch(e){console.error("Failed to load backgrounds:",e)}finally{h(!1)}})()},[]),(0,l.useEffect)(()=>{let e=a;"all"!==i&&(e=e.filter(e=>e.category===i)),d&&(e=e.filter(e=>e.name.toLowerCase().includes(d.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(d.toLowerCase())))),n(e)},[a,i,d]),(0,s.jsxs)(ep.Zp,{className:"w-80",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsxs)(ep.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ec.A,{className:"h-4 w-4"}),"背景圖庫"]})}),(0,s.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eT.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(f.p,{placeholder:"搜尋背景...",value:d,onChange:e=>o(e.target.value),className:"pl-10"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:eD.map(e=>(0,s.jsx)(b,{variant:i===e.id?"default":"outline",className:"cursor-pointer text-xs",onClick:()=>c(e.id),children:e.name},e.id))}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2 max-h-96 overflow-y-auto",children:u?(0,s.jsx)("div",{className:"col-span-2 text-center py-8 text-sm text-gray-500",children:"載入中..."}):0===r.length?(0,s.jsx)("div",{className:"col-span-2 text-center py-8 text-sm text-gray-500",children:"沒有找到符合條件的背景"}):r.map(a=>(0,s.jsxs)("div",{className:"group relative cursor-pointer rounded overflow-hidden border border-gray-200 hover:border-blue-300 transition-colors",onClick:()=>{e({type:"image",value:a.url})},children:[(0,s.jsx)("img",{src:a.url,alt:a.name,className:"w-full h-20 object-cover group-hover:scale-105 transition-transform"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2",children:(0,s.jsx)("p",{className:"text-white text-xs font-medium truncate",children:a.name})})]},a.id))}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["顯示 ",r.length," 個背景"]})]})]})}var eP=t(9954);let eO=[{id:"all",name:"全部",count:0},{id:"flowers",name:"花朵",count:0},{id:"animals",name:"動物",count:0},{id:"decorations",name:"裝飾",count:0},{id:"icons",name:"圖標",count:0},{id:"shapes",name:"形狀",count:0},{id:"seasonal",name:"節慶",count:0}],e$=[{id:"1",name:"櫻花",url:"https://images.unsplash.com/photo-1522383225653-ed111181a951?w=200&h=200&fit=crop",category:"flowers",tags:["櫻花","花朵","粉色","春天"]},{id:"2",name:"愛心",url:"https://images.unsplash.com/photo-1518199266791-5375a83190b7?w=200&h=200&fit=crop",category:"shapes",tags:["愛心","形狀","紅色","愛情"]},{id:"3",name:"蝴蝶",url:"https://images.unsplash.com/photo-1444927714506-8492d94b5ba0?w=200&h=200&fit=crop",category:"animals",tags:["蝴蝶","動物","昆蟲","彩色"]},{id:"4",name:"星星",url:"https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=200&h=200&fit=crop",category:"shapes",tags:["星星","形狀","夜空","閃亮"]},{id:"5",name:"太陽花",url:"https://images.unsplash.com/photo-1470509037663-253afd7f0f51?w=200&h=200&fit=crop",category:"flowers",tags:["太陽花","花朵","黃色","夏天"]},{id:"6",name:"禮物盒",url:"https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=200&h=200&fit=crop",category:"decorations",tags:["禮物","裝飾","慶祝","驚喜"]},{id:"7",name:"小鳥",url:"https://images.unsplash.com/photo-1444464666168-49d633b86797?w=200&h=200&fit=crop",category:"animals",tags:["小鳥","動物","飛行","自由"]},{id:"8",name:"雲朵",url:"https://images.unsplash.com/photo-1419833479618-c595710ac50c?w=200&h=200&fit=crop",category:"shapes",tags:["雲朵","形狀","天空","白色"]}];function eF(){let{addObjectLayer:e}=m(),[a,t]=(0,l.useState)([]),[r,n]=(0,l.useState)([]),[i,c]=(0,l.useState)("all"),[d,o]=(0,l.useState)(""),[u,h]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{(async()=>{h(!0);try{let e=e$.map(e=>({...e,type:"object",is_public:!0,created_at:new Date().toISOString()}));t(e),n(e)}catch(e){console.error("Failed to load objects:",e)}finally{h(!1)}})()},[]),(0,l.useEffect)(()=>{let e=a;"all"!==i&&(e=e.filter(e=>e.category===i)),d&&(e=e.filter(e=>e.name.toLowerCase().includes(d.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(d.toLowerCase())))),n(e)},[a,i,d]),(0,s.jsxs)(ep.Zp,{className:"w-80",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsxs)(ep.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(eP.A,{className:"h-4 w-4"}),"物件素材庫"]})}),(0,s.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eT.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(f.p,{placeholder:"搜尋物件...",value:d,onChange:e=>o(e.target.value),className:"pl-10"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:eO.map(e=>(0,s.jsx)(b,{variant:i===e.id?"default":"outline",className:"cursor-pointer text-xs",onClick:()=>c(e.id),children:e.name},e.id))}),(0,s.jsx)("div",{className:"grid grid-cols-3 gap-2 max-h-96 overflow-y-auto",children:u?(0,s.jsx)("div",{className:"col-span-3 text-center py-8 text-sm text-gray-500",children:"載入中..."}):0===r.length?(0,s.jsx)("div",{className:"col-span-3 text-center py-8 text-sm text-gray-500",children:"沒有找到符合條件的物件"}):r.map(a=>(0,s.jsxs)("div",{className:"group relative cursor-pointer rounded overflow-hidden border border-gray-200 hover:border-blue-300 transition-colors aspect-square",onClick:()=>{e(a.url,100,100)},children:[(0,s.jsx)("img",{src:a.url,alt:a.name,className:"w-full h-full object-cover group-hover:scale-105 transition-transform"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-1",children:(0,s.jsx)("p",{className:"text-white text-xs font-medium truncate",children:a.name})})]},a.id))}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["顯示 ",r.length," 個物件"]}),(0,s.jsx)("div",{className:"text-xs text-gray-500 bg-gray-50 p-2 rounded",children:"點擊物件即可新增到畫布中"})]})]})}var eU=t(3408),eB=t(7850),eW=t(5552),eZ=t(2568);function eY(){let{tool:e,canvasData:a,selectedObjectId:t,addObjectLayer:n,updateObjectLayer:i}=m(),[c,d]=(0,l.useState)(!1),o=(0,l.useRef)(null),u=a.objects.find(e=>e.id===t),h=async e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){let e=new FileReader;e.onload=e=>{var a;n(null==(a=e.target)?void 0:a.result,100,100)},e.readAsDataURL(t)}},p=async()=>{if(u){d(!0);try{setTimeout(()=>{console.log("AI background removal completed"),d(!1)},3e3)}catch(e){console.error("AI background removal failed:",e),d(!1)}}},f=(e,a)=>{u&&i(u.id,{[e]:a})},j=e=>{u&&("horizontal"===e?i(u.id,{flipX:!u.flipX}):i(u.id,{flipY:!u.flipY}))};return"object"!==e?null:(0,s.jsxs)(ep.Zp,{className:"w-80",children:[(0,s.jsx)(ep.aR,{children:(0,s.jsxs)(ep.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ec.A,{className:"h-4 w-4"}),"物件工具"]})}),(0,s.jsxs)(ep.Wu,{children:[(0,s.jsxs)(Z,{defaultValue:"upload",className:"w-full",children:[(0,s.jsxs)(Y,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(q,{value:"upload",className:"text-xs",children:"上傳"}),(0,s.jsx)(q,{value:"library",className:"text-xs",children:"素材庫"})]}),(0,s.jsxs)(V,{value:"upload",className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"上傳圖片"}),(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("input",{ref:o,type:"file",accept:"image/*",onChange:h,className:"hidden"}),(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>{var e;return null==(e=o.current)?void 0:e.click()},className:"w-full flex items-center gap-2",children:[(0,s.jsx)(eE.A,{className:"h-4 w-4"}),"選擇圖片檔案"]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"支援 JPG, PNG 格式，建議使用透明背景的 PNG 圖片"})]}),u&&(0,s.jsxs)("div",{children:[(0,s.jsx)(g.J,{className:"text-sm",children:"AI 去背"}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)(x.$,{variant:"outline",onClick:p,disabled:c,className:"w-full flex items-center gap-2",children:[(0,s.jsx)(eU.A,{className:"h-4 w-4"}),c?"處理中...":"自動去背"]})}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"使用 AI 自動移除選中物件的背景"})]})]}),(0,s.jsx)(V,{value:"library",className:"space-y-4",children:(0,s.jsx)(eF,{})})]}),u&&(0,s.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-3",children:"物件屬性"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{className:"text-sm",children:["寬度: ",Math.round(u.width),"px"]}),(0,s.jsx)(B,{value:[u.width],onValueChange:e=>{let[a]=e;return f("width",a)},min:10,max:800,step:1,className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{className:"text-sm",children:["高度: ",Math.round(u.height),"px"]}),(0,s.jsx)(B,{value:[u.height],onValueChange:e=>{let[a]=e;return f("height",a)},min:10,max:600,step:1,className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{className:"text-sm",children:["旋轉: ",Math.round(u.rotation),"\xb0"]}),(0,s.jsx)(B,{value:[u.rotation],onValueChange:e=>{let[a]=e;return f("rotation",a)},min:-180,max:180,step:1,className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(g.J,{className:"text-sm",children:["透明度: ",Math.round(100*u.opacity),"%"]}),(0,s.jsx)(B,{value:[u.opacity],onValueChange:e=>{let[a]=e;return f("opacity",a)},min:0,max:1,step:.01,className:"mt-1"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)(g.J,{className:"text-sm",children:"變形控制"}),(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>j("horizontal"),className:(0,r.cn)("flex items-center gap-1",u.flipX&&"bg-blue-100 text-blue-700"),title:"水平翻轉",children:(0,s.jsx)(eB.A,{className:"h-3 w-3"})}),(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>j("vertical"),className:(0,r.cn)("flex items-center gap-1",u.flipY&&"bg-blue-100 text-blue-700"),title:"垂直翻轉",children:(0,s.jsx)(eW.A,{className:"h-3 w-3"})}),(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>f("rotation",0),title:"重置旋轉",children:(0,s.jsx)(eZ.A,{className:"h-3 w-3"})}),(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{if(u){let e=u.opacity>0?0:1;i(u.id,{opacity:e})}},title:u.opacity>0?"隱藏":"顯示",children:u.opacity>0?(0,s.jsx)(Q.A,{className:"h-3 w-3"}):(0,s.jsx)(ef.A,{className:"h-3 w-3"})})]})]}),(0,s.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:["位置: (",Math.round(u.x),", ",Math.round(u.y),")"]})]}),!u&&(0,s.jsxs)("div",{className:"mt-6 text-sm text-gray-500 bg-gray-50 p-3 rounded",children:[(0,s.jsx)("p",{children:"• 上傳圖片來新增物件"}),(0,s.jsx)("p",{children:"• 選擇物件來編輯屬性"}),(0,s.jsx)("p",{children:"• 在畫布上拖拽來移動物件"}),(0,s.jsx)("p",{children:"• 使用控制點來調整大小"})]})]})]})}function eq(e){let{onSave:a,onExport:t}=e;return(0,s.jsx)(o,{children:(0,s.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[(0,s.jsx)(ex,{onSave:a,onExport:t}),(0,s.jsxs)("div",{className:"flex-1 flex",children:[(0,s.jsx)("div",{className:"w-80 border-r border-gray-200 bg-white p-4 overflow-y-auto",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(eC,{}),(0,s.jsx)(eS,{}),(0,s.jsx)(eR,{}),(0,s.jsx)(eJ,{}),(0,s.jsx)(eY,{})]})}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center p-4",children:(0,s.jsx)(h,{})}),(0,s.jsx)("div",{className:"w-64 border-l border-gray-200 bg-white p-4",children:(0,s.jsx)(eg,{})})]})]})})}},7606:(e,a,t)=>{"use strict";t.d(a,{AuthProvider:()=>i,A:()=>c});var s=t(2115),l=t(2099);let r={async getProfile(e){let a=(0,l.i)(),{data:t,error:s}=await a.from("profiles").select("*").eq("id",e).single();if(s)throw s;return t},async updateProfile(e,a){let t=(0,l.i)(),{data:s,error:r}=await t.from("profiles").update(a).eq("id",e).select().single();if(r)throw r;return s},async getUserTemplates(e){let a=(0,l.i)(),{data:t,error:s}=await a.from("templates").select("*").eq("author_id",e).order("created_at",{ascending:!1});if(s)throw s;return t},async getUserFavorites(e){let a=(0,l.i)(),{data:t,error:s}=await a.from("user_favorites").select("\n        *,\n        templates (\n          *,\n          profiles:author_id (\n            id,\n            username,\n            full_name,\n            avatar_url\n          )\n        )\n      ").eq("user_id",e).order("created_at",{ascending:!1});if(s)throw s;return t}},n=(0,s.createContext)(void 0);function i(e){let{children:a}=e,[t,i]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[o,m]=(0,s.useState)(!0),u=(0,l.i)();(0,s.useEffect)(()=>{(async()=>{var e;let{data:{session:a}}=await u.auth.getSession();if(i(null!=(e=null==a?void 0:a.user)?e:null),null==a?void 0:a.user)try{let e=await r.getProfile(a.user.id);d(e)}catch(e){console.error("Error fetching user profile:",e)}m(!1)})();let{data:{subscription:e}}=u.auth.onAuthStateChange(async(e,a)=>{var t;if(i(null!=(t=null==a?void 0:a.user)?t:null),null==a?void 0:a.user)try{let e=await r.getProfile(a.user.id);d(e)}catch(e){console.error("Error fetching user profile:",e),d(null)}else d(null);m(!1)});return()=>e.unsubscribe()},[u.auth]);let h=async(e,a)=>{let{error:t}=await u.auth.signInWithPassword({email:e,password:a});if(t)throw t},x=async(e,a,t)=>{let{error:s}=await u.auth.signUp({email:e,password:a,options:{data:t}});if(s)throw s},p=async()=>{let{error:e}=await u.auth.signOut();if(e)throw e},f=async e=>{if(!t)throw Error("No user logged in");d(await r.updateProfile(t.id,e))};return(0,s.createElement)(n.Provider,{value:{user:t,profile:c,loading:o,signIn:h,signUp:x,signOut:p,updateProfile:f}},a)}function c(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},9434:(e,a,t)=>{"use strict";t.d(a,{$C:()=>n,cn:()=>r});var s=t(2596),l=t(9688);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,l.QP)((0,s.$)(a))}function n(){return Math.random().toString(36).substr(2,9)}},9816:(e,a,t)=>{Promise.resolve().then(t.bind(t,353)),Promise.resolve().then(t.bind(t,7369))}},e=>{e.O(0,[413,865,203,441,964,358],()=>e(e.s=9816)),_N_E=e.O()}]);